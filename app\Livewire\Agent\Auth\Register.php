<?php

namespace App\Livewire\Agent\Auth;

use Livewire\Component;
use App\Models\AgentForm;
use Livewire\WithFileUploads;
use Livewire\Attributes\Computed;

class Register extends Component
{
    use WithFileUploads;

    public $first_name;
    public $last_name;
    public $email;
    public $country;
    public $gender;
    public $phone;
    public $username;
    public $password;
    public $password_confirmation;

    public $verification = [];

    #[Computed]
    public function getVerificationInfo()
    {
        return AgentForm::first()?->fields ?? [];
    }

    public function mount()
    {
        $this->country = getLocation()->name . ':' . getLocation()->dial_code;
    }

    public function registerNow() {}

    public function render()
    {
        return view('frontend::agent.auth.forms.register', [
            'countries' => getCountries(),
            'location' => getLocation(),
        ]);
    }
}
