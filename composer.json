{"name": "laravel/laravel", "type": "project", "description": "The skeleton application for the Laravel framework.", "keywords": ["laravel", "framework"], "license": "MIT", "require": {"php": "^8.2", "barryvdh/laravel-dompdf": "^3.0", "jenssegers/agent": "^2.6", "laravel/framework": "^12.0", "laravel/sanctum": "^4.0", "laravel/scout": "^10.11", "laravel/socialite": "^5.16", "laravel/tinker": "^2.9", "livewire/livewire": "^3.6", "mews/purifier": "^3.4", "pragmarx/google2fa-laravel": "^2.3", "pusher/pusher-php-server": "^7.2", "remotelywork/installer": "dev-main", "simplesoftwareio/simple-qrcode": "^4.2", "spatie/laravel-permission": "^6.9", "stripe/stripe-php": "^15.8", "twilio/sdk": "^7.3"}, "require-dev": {"barryvdh/laravel-debugbar": "^3.15", "elegantly/laravel-translator": "^2.4", "fakerphp/faker": "^1.23", "laravel/breeze": "^2.1", "laravel/pint": "^1.13", "laravel/sail": "^1.26", "mockery/mockery": "^1.6", "nunomaduro/collision": "^8.0", "pestphp/pest": "^3.8"}, "autoload": {"classmap": ["modules/Payment"], "psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}, "files": ["app/helpers.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "repositories": [{"type": "path", "url": "./modules/installer", "options": {"symlink": true}}], "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi", "@php -r \"file_exists('database/database.sqlite') || touch('database/database.sqlite');\"", "@php artisan migrate --graceful --ansi"], "dev": ["Composer\\Config::disableProcessTimeout", "npx concurrently -c \"#93c5fd,#c4b5fd,#fb7185,#fdba74\" \"php artisan serve\" \"php artisan queue:listen --tries=1\" \"php artisan pail --timeout=0\" \"npm run dev\" --names=server,queue,logs,vite"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "stable", "prefer-stable": true}