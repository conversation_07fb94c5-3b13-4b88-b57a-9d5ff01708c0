<div class="sign-up-box">
    <div class="signup-full">
        <div class="left">
            <div class="auth-form">
                <div class="title">
                    <h4>{{ __('Agent Registration') }}</h4>
                </div>
                <form wire:submit="registerNow">
                    <div class="content">
                        <div class="content-separation">
                            <p>{{ __('Basic information') }}</p>
                        </div>
                        <div class="row g-4">
                            <div class="col-md-6">
                                <div class="td-form-group">
                                    <label class="input-label">{{ __('First Name') }} <span>*</span></label>
                                    <div class="input-field">
                                        <input type="text" class="form-control" wire:model="first_name">
                                    </div>
                                    @error('first_name')
                                        <p class="feedback-invalid">{{ $message }}</p>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="td-form-group">
                                    <label class="input-label">{{ __('Last Name') }} <span>*</span></label>
                                    <div class="input-field">
                                        <input type="text" class="form-control" wire:model="last_name">
                                    </div>
                                    @error('last_name')
                                        <p class="feedback-invalid">{{ $message }}</p>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="td-form-group">
                                    <label class="input-label">{{ __('Email Address') }} <span>*</span></label>
                                    <div class="input-field">
                                        <input type="email" class="form-control" wire:model="email">
                                    </div>
                                    @error('email')
                                        <p class="feedback-invalid">{{ $message }}</p>
                                    @enderror
                                </div>
                            </div>
                            @if (getPageSetting('agent_username_show'))
                                <div class="col-md-6">
                                    <div class="td-form-group">
                                        <label class="input-label">
                                            {{ __('Username') }}
                                            @if (getPageSetting('agent_username_validation'))
                                                <span>*</span>
                                            @endif
                                        </label>
                                        <div class="input-field">
                                            <input type="text" class="form-control" wire:model="username">
                                        </div>
                                        @error('username')
                                            <p class="feedback-invalid">{{ $message }}</p>
                                        @enderror
                                    </div>
                                </div>
                            @endif
                            @if (getPageSetting('agent_country_show'))
                                <div class="col-md-6">
                                    <div class="td-form-group">
                                        <label class="input-label">{{ __('Country') }}
                                            @if (getPageSetting('agent_country_validation'))
                                                <span>*</span>
                                            @endif
                                        </label>
                                        <div class="common-nice-select common-nice-select-border-2 full-width w-100"
                                            wire:ignore>
                                            <select class="nice-select-active w-100 country-select"
                                                wire:model.live="country">
                                                @foreach ($countries as $country)
                                                    <option value="{{ $country['name'] }}:{{ $country['dial_code'] }}"
                                                        data-flag="{{ $country['flag'] }}"
                                                        data-code="{{ $country['dial_code'] }}"
                                                        @selected(old('country', data_get($location, 'name') . ':' . data_get($location, 'dial_code')) == $country['name'] . ':' . $country['dial_code'])>
                                                        {{ $country['name'] }}</option>
                                                @endforeach
                                            </select>
                                        </div>
                                        @error('country')
                                            <p class="feedback-invalid">{{ $message }}</p>
                                        @enderror
                                    </div>
                                </div>
                            @endif
                            @if (getPageSetting('agent_gender_show'))
                                <div class="col-md-6">
                                    <div class="td-form-group">
                                        <label class="input-label">{{ __('Gender') }}
                                            @if (getPageSetting('agent_gender_validation'))
                                                <span>*</span>
                                            @endif
                                        </label>
                                        <div class="common-nice-select common-nice-select-border-2 full-width w-100"
                                            wire:ignore>
                                            <select class="nice-select-active w-100 gender-select"
                                                wire:model.live="gender">
                                                <option value="male" @selected(old('gender') == 'male')>
                                                    {{ __('Male') }}
                                                </option>
                                                <option value="female" @selected(old('gender') == 'female')>
                                                    {{ __('Female') }}
                                                </option>
                                                <option value="other" @selected(old('gender') == 'other')>
                                                    {{ __('Other') }}
                                                </option>
                                            </select>
                                        </div>
                                        @error('gender')
                                            <p class="feedback-invalid">{{ $message }}</p>
                                        @enderror
                                    </div>
                                </div>
                            @endif
                            @if (getPageSetting('agent_phone_show'))
                                <div class="col-md-6">
                                    <div class="td-form-group">
                                        <label class="input-label">{{ __('Phone Number') }}
                                            @if (getPageSetting('agent_phone_validation'))
                                                <span>*</span>
                                            @endif
                                        </label>
                                        <div class="input-field has-left-text has-left-text-2">
                                            <input type="text" class="form-control" wire:model="phone">
                                            <div class="left-text">
                                                <div class="text"
                                                    x-text="$wire.country.split(':')[1]?.replace('*', '') || ''"></div>
                                            </div>
                                        </div>
                                        @error('phone')
                                            <p class="feedback-invalid">{{ $message }}</p>
                                        @enderror
                                    </div>
                                </div>
                            @endif
                            <div class="col-md-6">
                                <div class="td-form-group">
                                    <label class="input-label">{{ __('Password') }} <span>*</span></label>
                                    <div class="input-field">
                                        <input type="password" class="form-control" wire:model="password">
                                    </div>
                                    @error('password')
                                        <p class="feedback-invalid">{{ $message }}</p>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="td-form-group">
                                    <label class="input-label">{{ __('Confirm Password') }} <span>*</span></label>
                                    <div class="input-field">
                                        <input type="password" class="form-control" wire:model="password_confirmation">
                                    </div>
                                    @error('password_confirmation')
                                        <p class="feedback-invalid">{{ $message }}</p>
                                    @enderror
                                </div>
                            </div>
                        </div>
                        <div class="content-separation mt-5">
                            <p>{{ __('Verification information') }}</p>
                        </div>
                        <div class="row g-4">
                            @foreach ($this->getVerificationInfo() as $fieldKey => $field)
                                <div class="col-12">
                                    @if ($field['type'] == 'text')
                                        <div class="td-form-group">
                                            <label class="input-label">{{ $field['name'] }} <span>*</span></label>
                                            <div class="input-field">
                                                <input type="text" class="form-control"
                                                    wire:model="verification.{{ $fieldKey }}">
                                            </div>
                                            @error('verification.' . $field['name'])
                                                <p class="feedback-invalid">{{ $message }}</p>
                                            @enderror
                                        </div>
                                    @elseif ($field['type'] == 'file')
                                        <div class="common-upload-image-system">
                                            <div class="title">
                                                <div class="left">
                                                    <p>{{ $field['name'] }}<sup>*</sup></p>
                                                </div>
                                            </div>
                                            <div class="my-2">
                                                @include('frontend.default.include._file_uploader', [
                                                    'label' => $field['name'],
                                                    'required' => true,
                                                    'multiple' => false,
                                                    'wire:model' => 'verification.' . $fieldKey,
                                                ])
                                            </div>
                                        </div>
                                    @elseif($field['type'] == 'textarea')
                                        <div class="td-form-group">
                                            <label class="input-label">{{ $field['name'] }} <span>*</span></label>
                                            <div class="input-field">
                                                <textarea class="form-control" wire:model="verification.{{ $fieldKey }}"></textarea>
                                            </div>
                                            @error('verification.' . $field['name'])
                                                <p class="feedback-invalid">{{ $message }}</p>
                                            @enderror
                                        </div>
                                    @endif
                                </div>
                            @endforeach
                            <div class="col-12">
                                <div class="action-btn">
                                    <button type="submit" class="primary-button xl-btn w-100"
                                        wire:loading.attr="disabled" wire:target="registerNow" wire:loading.remove>
                                        {{ __('Create Account') }}
                                    </button>
                                    <button type="button" class="primary-button xl-btn w-100" disabled wire:loading
                                        wire:target="registerNow">
                                        {{ __('Creating Account...') }}
                                    </button>
                                </div>
                            </div>
                            <div class="col-12">
                                <div class="auth-switch">
                                    <p>{{ __('Already have an account?') }} <a
                                            href="{{ route('agent.login') }}">{{ __('Sign in') }}</a></p>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

@script
    <script>
        $('.country-select').on('change', function() {
            let value = $(this).val();
            @this.set('country', value);
        });

        $('.gender-select').on('change', function() {
            let value = $(this).val();
            @this.set('gender', value);
        });
    </script>
@endscript
