{"__meta": {"id": "01JYJNGC3154CF7P2WKABC09TS", "datetime": "2025-06-25 10:28:29", "utime": **********.665497, "method": "POST", "uri": "/livewire/update", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.019418, "end": **********.665507, "duration": 0.6460890769958496, "duration_str": "646ms", "measures": [{"label": "Booting", "start": **********.019418, "relative_start": 0, "end": **********.261811, "relative_end": **********.261811, "duration": 0.*****************, "duration_str": "242ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.261819, "relative_start": 0.****************, "end": **********.665509, "relative_end": 1.9073486328125e-06, "duration": 0.****************, "duration_str": "404ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.274724, "relative_start": 0.****************, "end": **********.276613, "relative_end": **********.276613, "duration": 0.0018889904022216797, "duration_str": "1.89ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: frontend::agent.auth.forms.register", "start": **********.635903, "relative_start": 0.****************, "end": **********.635903, "relative_end": **********.635903, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-filepond::upload", "start": **********.654645, "relative_start": 0.****************, "end": **********.654645, "relative_end": **********.654645, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-filepond::upload", "start": **********.662098, "relative_start": 0.****************, "end": **********.662098, "relative_end": **********.662098, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.663688, "relative_start": 0.6442699432373047, "end": **********.664242, "relative_end": **********.664242, "duration": 0.0005540847778320312, "duration_str": "554μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 36031464, "peak_usage_str": "34MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.19.3", "PHP Version": "8.3.13", "Environment": "local", "Debug Mode": "Enabled", "URL": "remitify.test", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 3, "nb_templates": 3, "templates": [{"name": "frontend::agent.auth.forms.register", "param_count": null, "params": [], "start": **********.635871, "type": "blade", "hash": "bladeC:\\laragon\\www\\remitify\\app\\Providers/../../resources/views/frontend/default/agent/auth/forms/register.blade.phpfrontend::agent.auth.forms.register", "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fremitify%2Fresources%2Fviews%2Ffrontend%2Fdefault%2Fagent%2Fauth%2Fforms%2Fregister.blade.php:1", "ajax": false, "filename": "register.blade.php", "line": "?"}}, {"name": "livewire-filepond::upload", "param_count": null, "params": [], "start": **********.654618, "type": "blade", "hash": "bladeC:\\laragon\\www\\remitify\\resources\\views/vendor/livewire-filepond/upload.blade.phplivewire-filepond::upload", "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fremitify%2Fresources%2Fviews%2Fvendor%2Flivewire-filepond%2Fupload.blade.php:1", "ajax": false, "filename": "upload.blade.php", "line": "?"}}, {"name": "livewire-filepond::upload", "param_count": null, "params": [], "start": **********.662072, "type": "blade", "hash": "bladeC:\\laragon\\www\\remitify\\resources\\views/vendor/livewire-filepond/upload.blade.phplivewire-filepond::upload", "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fremitify%2Fresources%2Fviews%2Fvendor%2Flivewire-filepond%2Fupload.blade.php:1", "ajax": false, "filename": "upload.blade.php", "line": "?"}}]}, "queries": {"count": 11, "nb_statements": 11, "nb_visible_statements": 11, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.0035899999999999994, "accumulated_duration_str": "3.59ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `sessions` where `id` = '9zzkH57N3ClbDtxtBrJ1qDcWJzaLhV70SL6IRJQ6' limit 1", "type": "query", "params": [], "bindings": ["9zzkH57N3ClbDtxtBrJ1qDcWJzaLhV70SL6IRJQ6"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\laragon\\www\\remitify\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\remitify\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\remitify\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\remitify\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\laragon\\www\\remitify\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.280278, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\laragon\\www\\remitify\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fremitify%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php:96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "remitify", "explain": null, "start_percent": 0, "width_percent": 16.156}, {"sql": "select * from `languages` where `is_default` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/helpers.php", "file": "C:\\laragon\\www\\remitify\\app\\helpers.php", "line": 366}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\remitify\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "C:\\laragon\\www\\remitify\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\remitify\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php", "file": "C:\\laragon\\www\\remitify\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php", "line": 87}], "start": **********.284073, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "helpers.php:366", "source": {"index": 16, "namespace": null, "name": "app/helpers.php", "file": "C:\\laragon\\www\\remitify\\app\\helpers.php", "line": 366}, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fremitify%2Fapp%2Fhelpers.php:366", "ajax": false, "filename": "helpers.php", "line": "366"}, "connection": "remitify", "explain": null, "start_percent": 16.156, "width_percent": 14.763}, {"sql": "select * from `languages` where `status` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Providers/ViewServiceProvider.php", "file": "C:\\laragon\\www\\remitify\\app\\Providers\\ViewServiceProvider.php", "line": 62}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\laragon\\www\\remitify\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 162}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\laragon\\www\\remitify\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 178}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\laragon\\www\\remitify\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 189}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\laragon\\www\\remitify\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 160}], "start": **********.63437, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "ViewServiceProvider.php:62", "source": {"index": 15, "namespace": null, "name": "app/Providers/ViewServiceProvider.php", "file": "C:\\laragon\\www\\remitify\\app\\Providers\\ViewServiceProvider.php", "line": 62}, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fremitify%2Fapp%2FProviders%2FViewServiceProvider.php:62", "ajax": false, "filename": "ViewServiceProvider.php", "line": "62"}, "connection": "remitify", "explain": null, "start_percent": 30.919, "width_percent": 13.092}, {"sql": "select * from `page_settings` where `key` = 'agent_username_show' limit 1", "type": "query", "params": [], "bindings": ["agent_username_show"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/helpers.php", "file": "C:\\laragon\\www\\remitify\\app\\helpers.php", "line": 101}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\remitify\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\remitify\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\remitify\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\laragon\\www\\remitify\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.637373, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "helpers.php:101", "source": {"index": 16, "namespace": null, "name": "app/helpers.php", "file": "C:\\laragon\\www\\remitify\\app\\helpers.php", "line": 101}, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fremitify%2Fapp%2Fhelpers.php:101", "ajax": false, "filename": "helpers.php", "line": "101"}, "connection": "remitify", "explain": null, "start_percent": 44.011, "width_percent": 11.421}, {"sql": "select * from `page_settings` where `key` = 'agent_username_validation' limit 1", "type": "query", "params": [], "bindings": ["agent_username_validation"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/helpers.php", "file": "C:\\laragon\\www\\remitify\\app\\helpers.php", "line": 101}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\remitify\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\remitify\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\remitify\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\laragon\\www\\remitify\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.638769, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "helpers.php:101", "source": {"index": 16, "namespace": null, "name": "app/helpers.php", "file": "C:\\laragon\\www\\remitify\\app\\helpers.php", "line": 101}, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fremitify%2Fapp%2Fhelpers.php:101", "ajax": false, "filename": "helpers.php", "line": "101"}, "connection": "remitify", "explain": null, "start_percent": 55.432, "width_percent": 6.964}, {"sql": "select * from `page_settings` where `key` = 'agent_country_show' limit 1", "type": "query", "params": [], "bindings": ["agent_country_show"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/helpers.php", "file": "C:\\laragon\\www\\remitify\\app\\helpers.php", "line": 101}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\remitify\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\remitify\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\remitify\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\laragon\\www\\remitify\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.6398659, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "helpers.php:101", "source": {"index": 16, "namespace": null, "name": "app/helpers.php", "file": "C:\\laragon\\www\\remitify\\app\\helpers.php", "line": 101}, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fremitify%2Fapp%2Fhelpers.php:101", "ajax": false, "filename": "helpers.php", "line": "101"}, "connection": "remitify", "explain": null, "start_percent": 62.396, "width_percent": 6.685}, {"sql": "select * from `page_settings` where `key` = 'agent_gender_show' limit 1", "type": "query", "params": [], "bindings": ["agent_gender_show"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/helpers.php", "file": "C:\\laragon\\www\\remitify\\app\\helpers.php", "line": 101}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\remitify\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\remitify\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\remitify\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\laragon\\www\\remitify\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.640954, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "helpers.php:101", "source": {"index": 16, "namespace": null, "name": "app/helpers.php", "file": "C:\\laragon\\www\\remitify\\app\\helpers.php", "line": 101}, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fremitify%2Fapp%2Fhelpers.php:101", "ajax": false, "filename": "helpers.php", "line": "101"}, "connection": "remitify", "explain": null, "start_percent": 69.081, "width_percent": 6.685}, {"sql": "select * from `page_settings` where `key` = 'agent_gender_validation' limit 1", "type": "query", "params": [], "bindings": ["agent_gender_validation"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/helpers.php", "file": "C:\\laragon\\www\\remitify\\app\\helpers.php", "line": 101}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\remitify\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\remitify\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\remitify\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\laragon\\www\\remitify\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.6421258, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "helpers.php:101", "source": {"index": 16, "namespace": null, "name": "app/helpers.php", "file": "C:\\laragon\\www\\remitify\\app\\helpers.php", "line": 101}, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fremitify%2Fapp%2Fhelpers.php:101", "ajax": false, "filename": "helpers.php", "line": "101"}, "connection": "remitify", "explain": null, "start_percent": 75.766, "width_percent": 6.685}, {"sql": "select * from `page_settings` where `key` = 'agent_phone_show' limit 1", "type": "query", "params": [], "bindings": ["agent_phone_show"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/helpers.php", "file": "C:\\laragon\\www\\remitify\\app\\helpers.php", "line": 101}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\remitify\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\remitify\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\remitify\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\laragon\\www\\remitify\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.643493, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "helpers.php:101", "source": {"index": 16, "namespace": null, "name": "app/helpers.php", "file": "C:\\laragon\\www\\remitify\\app\\helpers.php", "line": 101}, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fremitify%2Fapp%2Fhelpers.php:101", "ajax": false, "filename": "helpers.php", "line": "101"}, "connection": "remitify", "explain": null, "start_percent": 82.451, "width_percent": 5.292}, {"sql": "select * from `page_settings` where `key` = 'agent_phone_validation' limit 1", "type": "query", "params": [], "bindings": ["agent_phone_validation"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/helpers.php", "file": "C:\\laragon\\www\\remitify\\app\\helpers.php", "line": 101}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\remitify\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\remitify\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\remitify\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\laragon\\www\\remitify\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.644549, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "helpers.php:101", "source": {"index": 16, "namespace": null, "name": "app/helpers.php", "file": "C:\\laragon\\www\\remitify\\app\\helpers.php", "line": 101}, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fremitify%2Fapp%2Fhelpers.php:101", "ajax": false, "filename": "helpers.php", "line": "101"}, "connection": "remitify", "explain": null, "start_percent": 87.744, "width_percent": 6.128}, {"sql": "select * from `agent_forms` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Livewire/Agent/Auth/Register.php", "file": "C:\\laragon\\www\\remitify\\app\\Livewire\\Agent\\Auth\\Register.php", "line": 30}, {"index": 20, "namespace": "view", "name": "frontend::agent.auth.forms.register", "file": "C:\\laragon\\www\\remitify\\app\\Providers/../../resources/views/frontend/default/agent/auth/forms/register.blade.php", "line": 239}, {"index": 22, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\remitify\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\remitify\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\remitify\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.6458929, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "Register.php:30", "source": {"index": 19, "namespace": null, "name": "app/Livewire/Agent/Auth/Register.php", "file": "C:\\laragon\\www\\remitify\\app\\Livewire\\Agent\\Auth\\Register.php", "line": 30}, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fremitify%2Fapp%2FLivewire%2FAgent%2FAuth%2FRegister.php:30", "ajax": false, "filename": "Register.php", "line": "30"}, "connection": "remitify", "explain": null, "start_percent": 93.872, "width_percent": 6.128}]}, "models": {"data": {"App\\Models\\PageSetting": {"value": 7, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fremitify%2Fapp%2FModels%2FPageSetting.php:1", "ajax": false, "filename": "PageSetting.php", "line": "?"}}, "App\\Models\\Language": {"value": 1, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fremitify%2Fapp%2FModels%2FLanguage.php:1", "ajax": false, "filename": "Language.php", "line": "?"}}, "App\\Models\\AgentForm": {"value": 1, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fremitify%2Fapp%2FModels%2FAgentForm.php:1", "ajax": false, "filename": "AgentForm.php", "line": "?"}}}, "count": 9, "is_counter": true}, "livewire": {"data": {"agent.auth.register #l5Av3X5nDNyS4zjcQqSx": "array:4 [\n  \"data\" => array:10 [\n    \"first_name\" => null\n    \"last_name\" => null\n    \"email\" => null\n    \"country\" => \"Bangladesh:+880\"\n    \"gender\" => null\n    \"phone\" => null\n    \"username\" => null\n    \"password\" => null\n    \"password_confirmation\" => null\n    \"verification\" => array:1 [\n      2 => Livewire\\Features\\SupportFileUploads\\TemporaryUploadedFile {#2402\n        -originalName: \"OrIHIDNVYKvAZrfrTpevntD5lJZYNm-metaaGVhZGVycy5wbmc=-.png\"\n        -mimeType: \"application/octet-stream\"\n        -error: 0\n        -originalPath: \"livewire-tmp/OrIHIDNVYKvAZrfrTpevntD5lJZYNm-metaaGVhZGVycy5wbmc=-.png\"\n        -test: false\n        #hashName: null\n        #disk: \"local\"\n        #storage: Illuminate\\Filesystem\\LocalFilesystemAdapter {#2289\n          #driver: League\\Flysystem\\Filesystem {#2290\n            -config: League\\Flysystem\\Config {#2291\n              -options: []\n            }\n            -pathNormalizer: League\\Flysystem\\WhitespacePathNormalizer {#2416}\n            -adapter: League\\Flysystem\\Local\\LocalFilesystemAdapter {#2412\n              -prefixer: League\\Flysystem\\PathPrefixer {#2413\n                -prefix: \"C:\\laragon\\www\\remitify\\storage\\app\\\"\n                -separator: \"\\\"\n              }\n              -visibility: League\\Flysystem\\UnixVisibility\\PortableVisibilityConverter {#2411\n                -filePublic: 420\n                -filePrivate: 384\n                -directoryPublic: 493\n                -directoryPrivate: 448\n                -defaultForDirectories: \"private\"\n              }\n              -mimeTypeDetector: League\\Flysystem\\Local\\FallbackMimeTypeDetector {#2409\n                -detector: League\\MimeTypeDetection\\FinfoMimeTypeDetector {#2408\n                  -finfo: finfo {#2287}\n                  -extensionMap: League\\MimeTypeDetection\\GeneratedExtensionToMimeTypeMap {#2288}\n                  -bufferSampleSize: null\n                  -inconclusiveMimetypes: array:5 [\n                    0 => \"application/x-empty\"\n                    1 => \"text/plain\"\n                    2 => \"text/x-asm\"\n                    3 => \"application/octet-stream\"\n                    4 => \"inode/x-empty\"\n                  ]\n                }\n                -inconclusiveMimetypes: array:5 [\n                  0 => \"application/x-empty\"\n                  1 => \"text/plain\"\n                  2 => \"text/x-asm\"\n                  3 => \"application/octet-stream\"\n                  4 => \"inode/x-empty\"\n                ]\n                -useInconclusiveMimeTypeFallback: false\n              }\n              -rootLocation: \"C:\\laragon\\www\\remitify\\storage\\app\"\n              -rootLocationIsSetup: true\n              -writeFlags: 2\n              -linkHandling: 2\n            }\n            -publicUrlGenerator: null\n            -temporaryUrlGenerator: null\n          }\n          #adapter: League\\Flysystem\\Local\\LocalFilesystemAdapter {#2412}\n          #config: array:3 [\n            \"driver\" => \"local\"\n            \"root\" => \"C:\\laragon\\www\\remitify\\storage\\app\"\n            \"throw\" => false\n          ]\n          #prefixer: League\\Flysystem\\PathPrefixer {#2407\n            -prefix: \"C:\\laragon\\www\\remitify\\storage\\app\\\"\n            -separator: \"\\\"\n          }\n          #serveCallback: null\n          #temporaryUrlCallback: null\n          #disk: \"local\"\n          #shouldServeSignedUrls: false\n          #urlGeneratorResolver: Closure() {#2406\n            class: \"Illuminate\\Filesystem\\FilesystemManager\"\n            this: Illuminate\\Filesystem\\FilesystemManager {#2410 …}\n            file: \"C:\\laragon\\www\\remitify\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\FilesystemManager.php\"\n            line: \"193 to 193\"\n          }\n        }\n        #path: \"livewire-tmp/OrIHIDNVYKvAZrfrTpevntD5lJZYNm-metaaGVhZGVycy5wbmc=-.png\"\n        path: \"C:\\laragon\\www\\remitify\\storage\\app\\livewire-tmp\"\n        filename: \"OrIHIDNVYKvAZrfrTpevntD5lJZYNm-metaaGVhZGVycy5wbmc=-.png\"\n        basename: \"phpF9F1.tmp\"\n        pathname: \"C:\\laragon\\www\\remitify\\storage\\app\\livewire-tmp/OrIHIDNVYKvAZrfrTpevntD5lJZYNm-metaaGVhZGVycy5wbmc=-.png\"\n        extension: \"tmp\"\n        realPath: \"C:\\laragon\\www\\remitify\\storage\\app\\livewire-tmp/OrIHIDNVYKvAZrfrTpevntD5lJZYNm-metaaGVhZGVycy5wbmc=-.png\"\n        aTime: 2025-06-25 10:28:29\n        mTime: 2025-06-25 10:28:29\n        cTime: 2025-06-25 10:28:29\n        inode: 16044073672526222\n        size: 424086\n        writable: false\n        readable: false\n        executable: false\n        file: false\n        dir: false\n        link: false\n      }\n    ]\n  ]\n  \"name\" => \"agent.auth.register\"\n  \"component\" => \"App\\Livewire\\Agent\\Auth\\Register\"\n  \"id\" => \"l5Av3X5nDNyS4zjcQqSx\"\n]"}, "count": 1}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://remitify.test/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate", "uri": "POST livewire/update", "controller": "App\\Livewire\\Agent\\Auth\\Register@_finishUpload<a href=\"vscode://file/C%3A%2Flaragon%2Fwww%2Fremitify%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FFeatures%2FSupportFileUploads%2FWithFileUploads.php:28\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"vscode://file/C%3A%2Flaragon%2Fwww%2Fremitify%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FFeatures%2FSupportFileUploads%2FWithFileUploads.php:28\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/livewire/livewire/src/Features/SupportFileUploads/WithFileUploads.php:28-51</a>", "middleware": "web", "duration": "647ms", "peak_memory": "36MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1638197975 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1638197975\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1754323584 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">RMxF3qX3ZYTEIfPd2KsBSsaXkg5FlaxjW4jANBbe</span>\"\n  \"<span class=sf-dump-key>components</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"469 characters\">{&quot;data&quot;:{&quot;first_name&quot;:null,&quot;last_name&quot;:null,&quot;email&quot;:null,&quot;country&quot;:&quot;Bangladesh:+880&quot;,&quot;gender&quot;:null,&quot;phone&quot;:null,&quot;username&quot;:null,&quot;password&quot;:null,&quot;password_confirmation&quot;:null,&quot;verification&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}]},&quot;memo&quot;:{&quot;id&quot;:&quot;l5Av3X5nDNyS4zjcQqSx&quot;,&quot;name&quot;:&quot;agent.auth.register&quot;,&quot;path&quot;:&quot;agent\\/register&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[&quot;4169463714-0&quot;],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;18e9e306dd966adc131271dd8a185780081935a3c093a3933202ae04b4cb8696&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => []\n      \"<span class=sf-dump-key>calls</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"13 characters\">_finishUpload</span>\"\n          \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">verification.2</span>\"\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"57 characters\">/OrIHIDNVYKvAZrfrTpevntD5lJZYNm-metaaGVhZGVycy5wbmc=-.png</span>\"\n            </samp>]\n            <span class=sf-dump-index>2</span> => \"\"\n          </samp>]\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1754323584\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-389351366 data-indent-pad=\"  \"><span class=sf-dump-note>array:12</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"714 characters\">XSRF-TOKEN=eyJpdiI6IkNmNGJyZDJIdXFQSzJXTllsUXJhYWc9PSIsInZhbHVlIjoiQnYrek1Jc3pOTHc4djVPbzhZcldTUzlIaG5XNDNwa0p2VEo2cEs0bHBHRGR4czlQZkZ2Wm5QZ0hhbWliWVVObm4zZjBsZnpkNHZyTmVnQ1JQMmJlQTcvWURMa01naHNmNUtNM3RnTkRMWEtDeW1aeHpYUmMyWTJQNXd0VUJtOXciLCJtYWMiOiIxOTkwZTE2OWM2YzhkMWVhMDIwNWViODA1ZTY0MjYxY2VkMDg0ZWM4MWQzYjExNTFmNmZiN2QyZDI1ZjUyZmIyIiwidGFnIjoiIn0%3D; remitify_session=eyJpdiI6InlaZWo5QWNZQUZJNkk1aHZCbGxOVUE9PSIsInZhbHVlIjoiazhqRTd2Yy9TNzRiaUNzN3puOTlZZStBekFPTWR0bnRJY2hDSkJDaFZiNjZ4VnNBSit1NTkxZVR2eFFwWXR2RWtFdkFLT05LWUY5QUhMQTR0cFZBeGc4YlRLUFlEaStyN2FmY1krOENLVDhEMFFvMGhUZEdtdXhBVVJoREJ6aTUiLCJtYWMiOiI5YTdkMmU0M2ZkMDg2YzBjMmJlYjJmN2QzOTMzZmFjOGI2MWFmYzE2N2RlMjEyY2QyOGEzOGIzOTUyY2JiNWE5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,bn;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">http://remitify.test/agent/register</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://remitify.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">774</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">close</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">remitify.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-389351366\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-526091084 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">RMxF3qX3ZYTEIfPd2KsBSsaXkg5FlaxjW4jANBbe</span>\"\n  \"<span class=sf-dump-key>remitify_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9zzkH57N3ClbDtxtBrJ1qDcWJzaLhV70SL6IRJQ6</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-526091084\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1231276263 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 25 Jun 2025 04:28:29 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1231276263\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-142787273 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">RMxF3qX3ZYTEIfPd2KsBSsaXkg5FlaxjW4jANBbe</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"35 characters\">http://remitify.test/agent/register</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-142787273\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://remitify.test/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate"}, "badge": null}}