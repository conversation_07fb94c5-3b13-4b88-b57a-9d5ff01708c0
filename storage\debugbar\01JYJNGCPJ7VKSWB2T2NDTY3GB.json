{"__meta": {"id": "01JYJNGCPJ7VKSWB2T2NDTY3GB", "datetime": "2025-06-25 10:28:30", "utime": **********.29108, "method": "POST", "uri": "/livewire/update", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.696484, "end": **********.29109, "duration": 0.5946059226989746, "duration_str": "595ms", "measures": [{"label": "Booting", "start": **********.696484, "relative_start": 0, "end": **********.900146, "relative_end": **********.900146, "duration": 0.*****************, "duration_str": "204ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.900154, "relative_start": 0.*****************, "end": **********.291091, "relative_end": 9.5367431640625e-07, "duration": 0.*****************, "duration_str": "391ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.911902, "relative_start": 0.*****************, "end": **********.913962, "relative_end": **********.913962, "duration": 0.****************, "duration_str": "2.06ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: frontend::agent.auth.forms.register", "start": **********.259847, "relative_start": 0.****************, "end": **********.259847, "relative_end": **********.259847, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-filepond::upload", "start": **********.279636, "relative_start": 0.****************, "end": **********.279636, "relative_end": **********.279636, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-filepond::upload", "start": **********.287514, "relative_start": 0.****************, "end": **********.287514, "relative_end": **********.287514, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.289231, "relative_start": 0.5927469730377197, "end": **********.289784, "relative_end": **********.289784, "duration": 0.0005528926849365234, "duration_str": "553μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 34761928, "peak_usage_str": "33MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.19.3", "PHP Version": "8.3.13", "Environment": "local", "Debug Mode": "Enabled", "URL": "remitify.test", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 3, "nb_templates": 3, "templates": [{"name": "frontend::agent.auth.forms.register", "param_count": null, "params": [], "start": **********.25981, "type": "blade", "hash": "bladeC:\\laragon\\www\\remitify\\app\\Providers/../../resources/views/frontend/default/agent/auth/forms/register.blade.phpfrontend::agent.auth.forms.register", "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fremitify%2Fresources%2Fviews%2Ffrontend%2Fdefault%2Fagent%2Fauth%2Fforms%2Fregister.blade.php:1", "ajax": false, "filename": "register.blade.php", "line": "?"}}, {"name": "livewire-filepond::upload", "param_count": null, "params": [], "start": **********.279605, "type": "blade", "hash": "bladeC:\\laragon\\www\\remitify\\resources\\views/vendor/livewire-filepond/upload.blade.phplivewire-filepond::upload", "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fremitify%2Fresources%2Fviews%2Fvendor%2Flivewire-filepond%2Fupload.blade.php:1", "ajax": false, "filename": "upload.blade.php", "line": "?"}}, {"name": "livewire-filepond::upload", "param_count": null, "params": [], "start": **********.287487, "type": "blade", "hash": "bladeC:\\laragon\\www\\remitify\\resources\\views/vendor/livewire-filepond/upload.blade.phplivewire-filepond::upload", "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fremitify%2Fresources%2Fviews%2Fvendor%2Flivewire-filepond%2Fupload.blade.php:1", "ajax": false, "filename": "upload.blade.php", "line": "?"}}]}, "queries": {"count": 11, "nb_statements": 11, "nb_visible_statements": 11, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.0030600000000000002, "accumulated_duration_str": "3.06ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `sessions` where `id` = '9zzkH57N3ClbDtxtBrJ1qDcWJzaLhV70SL6IRJQ6' limit 1", "type": "query", "params": [], "bindings": ["9zzkH57N3ClbDtxtBrJ1qDcWJzaLhV70SL6IRJQ6"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\laragon\\www\\remitify\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\remitify\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\remitify\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\remitify\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\laragon\\www\\remitify\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.918812, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\laragon\\www\\remitify\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fremitify%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php:96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "remitify", "explain": null, "start_percent": 0, "width_percent": 16.993}, {"sql": "select * from `languages` where `is_default` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/helpers.php", "file": "C:\\laragon\\www\\remitify\\app\\helpers.php", "line": 366}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\remitify\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "C:\\laragon\\www\\remitify\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\remitify\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php", "file": "C:\\laragon\\www\\remitify\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php", "line": 87}], "start": **********.922493, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "helpers.php:366", "source": {"index": 16, "namespace": null, "name": "app/helpers.php", "file": "C:\\laragon\\www\\remitify\\app\\helpers.php", "line": 366}, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fremitify%2Fapp%2Fhelpers.php:366", "ajax": false, "filename": "helpers.php", "line": "366"}, "connection": "remitify", "explain": null, "start_percent": 16.993, "width_percent": 13.072}, {"sql": "select * from `languages` where `status` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Providers/ViewServiceProvider.php", "file": "C:\\laragon\\www\\remitify\\app\\Providers\\ViewServiceProvider.php", "line": 62}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\laragon\\www\\remitify\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 162}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\laragon\\www\\remitify\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 178}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\laragon\\www\\remitify\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 189}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\laragon\\www\\remitify\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 160}], "start": **********.257356, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "ViewServiceProvider.php:62", "source": {"index": 15, "namespace": null, "name": "app/Providers/ViewServiceProvider.php", "file": "C:\\laragon\\www\\remitify\\app\\Providers\\ViewServiceProvider.php", "line": 62}, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fremitify%2Fapp%2FProviders%2FViewServiceProvider.php:62", "ajax": false, "filename": "ViewServiceProvider.php", "line": "62"}, "connection": "remitify", "explain": null, "start_percent": 30.065, "width_percent": 17.32}, {"sql": "select * from `page_settings` where `key` = 'agent_username_show' limit 1", "type": "query", "params": [], "bindings": ["agent_username_show"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/helpers.php", "file": "C:\\laragon\\www\\remitify\\app\\helpers.php", "line": 101}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\remitify\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\remitify\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\remitify\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\laragon\\www\\remitify\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.2616038, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "helpers.php:101", "source": {"index": 16, "namespace": null, "name": "app/helpers.php", "file": "C:\\laragon\\www\\remitify\\app\\helpers.php", "line": 101}, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fremitify%2Fapp%2Fhelpers.php:101", "ajax": false, "filename": "helpers.php", "line": "101"}, "connection": "remitify", "explain": null, "start_percent": 47.386, "width_percent": 8.497}, {"sql": "select * from `page_settings` where `key` = 'agent_username_validation' limit 1", "type": "query", "params": [], "bindings": ["agent_username_validation"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/helpers.php", "file": "C:\\laragon\\www\\remitify\\app\\helpers.php", "line": 101}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\remitify\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\remitify\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\remitify\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\laragon\\www\\remitify\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.263279, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "helpers.php:101", "source": {"index": 16, "namespace": null, "name": "app/helpers.php", "file": "C:\\laragon\\www\\remitify\\app\\helpers.php", "line": 101}, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fremitify%2Fapp%2Fhelpers.php:101", "ajax": false, "filename": "helpers.php", "line": "101"}, "connection": "remitify", "explain": null, "start_percent": 55.882, "width_percent": 7.19}, {"sql": "select * from `page_settings` where `key` = 'agent_country_show' limit 1", "type": "query", "params": [], "bindings": ["agent_country_show"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/helpers.php", "file": "C:\\laragon\\www\\remitify\\app\\helpers.php", "line": 101}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\remitify\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\remitify\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\remitify\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\laragon\\www\\remitify\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.264375, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "helpers.php:101", "source": {"index": 16, "namespace": null, "name": "app/helpers.php", "file": "C:\\laragon\\www\\remitify\\app\\helpers.php", "line": 101}, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fremitify%2Fapp%2Fhelpers.php:101", "ajax": false, "filename": "helpers.php", "line": "101"}, "connection": "remitify", "explain": null, "start_percent": 63.072, "width_percent": 5.882}, {"sql": "select * from `page_settings` where `key` = 'agent_gender_show' limit 1", "type": "query", "params": [], "bindings": ["agent_gender_show"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/helpers.php", "file": "C:\\laragon\\www\\remitify\\app\\helpers.php", "line": 101}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\remitify\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\remitify\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\remitify\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\laragon\\www\\remitify\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.265405, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "helpers.php:101", "source": {"index": 16, "namespace": null, "name": "app/helpers.php", "file": "C:\\laragon\\www\\remitify\\app\\helpers.php", "line": 101}, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fremitify%2Fapp%2Fhelpers.php:101", "ajax": false, "filename": "helpers.php", "line": "101"}, "connection": "remitify", "explain": null, "start_percent": 68.954, "width_percent": 5.556}, {"sql": "select * from `page_settings` where `key` = 'agent_gender_validation' limit 1", "type": "query", "params": [], "bindings": ["agent_gender_validation"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/helpers.php", "file": "C:\\laragon\\www\\remitify\\app\\helpers.php", "line": 101}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\remitify\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\remitify\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\remitify\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\laragon\\www\\remitify\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.266447, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "helpers.php:101", "source": {"index": 16, "namespace": null, "name": "app/helpers.php", "file": "C:\\laragon\\www\\remitify\\app\\helpers.php", "line": 101}, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fremitify%2Fapp%2Fhelpers.php:101", "ajax": false, "filename": "helpers.php", "line": "101"}, "connection": "remitify", "explain": null, "start_percent": 74.51, "width_percent": 5.556}, {"sql": "select * from `page_settings` where `key` = 'agent_phone_show' limit 1", "type": "query", "params": [], "bindings": ["agent_phone_show"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/helpers.php", "file": "C:\\laragon\\www\\remitify\\app\\helpers.php", "line": 101}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\remitify\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\remitify\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\remitify\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\laragon\\www\\remitify\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.267595, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "helpers.php:101", "source": {"index": 16, "namespace": null, "name": "app/helpers.php", "file": "C:\\laragon\\www\\remitify\\app\\helpers.php", "line": 101}, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fremitify%2Fapp%2Fhelpers.php:101", "ajax": false, "filename": "helpers.php", "line": "101"}, "connection": "remitify", "explain": null, "start_percent": 80.065, "width_percent": 6.209}, {"sql": "select * from `page_settings` where `key` = 'agent_phone_validation' limit 1", "type": "query", "params": [], "bindings": ["agent_phone_validation"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/helpers.php", "file": "C:\\laragon\\www\\remitify\\app\\helpers.php", "line": 101}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\remitify\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\remitify\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\remitify\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\laragon\\www\\remitify\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.268671, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "helpers.php:101", "source": {"index": 16, "namespace": null, "name": "app/helpers.php", "file": "C:\\laragon\\www\\remitify\\app\\helpers.php", "line": 101}, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fremitify%2Fapp%2Fhelpers.php:101", "ajax": false, "filename": "helpers.php", "line": "101"}, "connection": "remitify", "explain": null, "start_percent": 86.275, "width_percent": 6.209}, {"sql": "select * from `agent_forms` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Livewire/Agent/Auth/Register.php", "file": "C:\\laragon\\www\\remitify\\app\\Livewire\\Agent\\Auth\\Register.php", "line": 30}, {"index": 20, "namespace": "view", "name": "frontend::agent.auth.forms.register", "file": "C:\\laragon\\www\\remitify\\app\\Providers/../../resources/views/frontend/default/agent/auth/forms/register.blade.php", "line": 239}, {"index": 22, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\remitify\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\remitify\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\remitify\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.270077, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "Register.php:30", "source": {"index": 19, "namespace": null, "name": "app/Livewire/Agent/Auth/Register.php", "file": "C:\\laragon\\www\\remitify\\app\\Livewire\\Agent\\Auth\\Register.php", "line": 30}, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fremitify%2Fapp%2FLivewire%2FAgent%2FAuth%2FRegister.php:30", "ajax": false, "filename": "Register.php", "line": "30"}, "connection": "remitify", "explain": null, "start_percent": 92.484, "width_percent": 7.516}]}, "models": {"data": {"App\\Models\\PageSetting": {"value": 7, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fremitify%2Fapp%2FModels%2FPageSetting.php:1", "ajax": false, "filename": "PageSetting.php", "line": "?"}}, "App\\Models\\Language": {"value": 1, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fremitify%2Fapp%2FModels%2FLanguage.php:1", "ajax": false, "filename": "Language.php", "line": "?"}}, "App\\Models\\AgentForm": {"value": 1, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fremitify%2Fapp%2FModels%2FAgentForm.php:1", "ajax": false, "filename": "AgentForm.php", "line": "?"}}}, "count": 9, "is_counter": true}, "livewire": {"data": {"agent.auth.register #l5Av3X5nDNyS4zjcQqSx": "array:4 [\n  \"data\" => array:10 [\n    \"first_name\" => null\n    \"last_name\" => null\n    \"email\" => null\n    \"country\" => \"Bangladesh:+880\"\n    \"gender\" => null\n    \"phone\" => null\n    \"username\" => null\n    \"password\" => null\n    \"password_confirmation\" => null\n    \"verification\" => array:1 [\n      2 => Livewire\\Features\\SupportFileUploads\\TemporaryUploadedFile {#1972\n        -originalName: \"OrIHIDNVYKvAZrfrTpevntD5lJZYNm-metaaGVhZGVycy5wbmc=-.png\"\n        -mimeType: \"application/octet-stream\"\n        -error: 0\n        -originalPath: \"livewire-tmp/OrIHIDNVYKvAZrfrTpevntD5lJZYNm-metaaGVhZGVycy5wbmc=-.png\"\n        -test: false\n        #hashName: null\n        #disk: \"local\"\n        #storage: Illuminate\\Filesystem\\LocalFilesystemAdapter {#1938\n          #driver: League\\Flysystem\\Filesystem {#2148\n            -config: League\\Flysystem\\Config {#2149\n              -options: []\n            }\n            -pathNormalizer: League\\Flysystem\\WhitespacePathNormalizer {#2150}\n            -adapter: League\\Flysystem\\Local\\LocalFilesystemAdapter {#2145\n              -prefixer: League\\Flysystem\\PathPrefixer {#1940\n                -prefix: \"C:\\laragon\\www\\remitify\\storage\\app\\\"\n                -separator: \"\\\"\n              }\n              -visibility: League\\Flysystem\\UnixVisibility\\PortableVisibilityConverter {#2146\n                -filePublic: 420\n                -filePrivate: 384\n                -directoryPublic: 493\n                -directoryPrivate: 448\n                -defaultForDirectories: \"private\"\n              }\n              -mimeTypeDetector: League\\Flysystem\\Local\\FallbackMimeTypeDetector {#2144\n                -detector: League\\MimeTypeDetection\\FinfoMimeTypeDetector {#1966\n                  -finfo: finfo {#2026}\n                  -extensionMap: League\\MimeTypeDetection\\GeneratedExtensionToMimeTypeMap {#2147}\n                  -bufferSampleSize: null\n                  -inconclusiveMimetypes: array:5 [\n                    0 => \"application/x-empty\"\n                    1 => \"text/plain\"\n                    2 => \"text/x-asm\"\n                    3 => \"application/octet-stream\"\n                    4 => \"inode/x-empty\"\n                  ]\n                }\n                -inconclusiveMimetypes: array:5 [\n                  0 => \"application/x-empty\"\n                  1 => \"text/plain\"\n                  2 => \"text/x-asm\"\n                  3 => \"application/octet-stream\"\n                  4 => \"inode/x-empty\"\n                ]\n                -useInconclusiveMimeTypeFallback: false\n              }\n              -rootLocation: \"C:\\laragon\\www\\remitify\\storage\\app\"\n              -rootLocationIsSetup: true\n              -writeFlags: 2\n              -linkHandling: 2\n            }\n            -publicUrlGenerator: null\n            -temporaryUrlGenerator: null\n          }\n          #adapter: League\\Flysystem\\Local\\LocalFilesystemAdapter {#2145}\n          #config: array:3 [\n            \"driver\" => \"local\"\n            \"root\" => \"C:\\laragon\\www\\remitify\\storage\\app\"\n            \"throw\" => false\n          ]\n          #prefixer: League\\Flysystem\\PathPrefixer {#2151\n            -prefix: \"C:\\laragon\\www\\remitify\\storage\\app\\\"\n            -separator: \"\\\"\n          }\n          #serveCallback: null\n          #temporaryUrlCallback: null\n          #disk: \"local\"\n          #shouldServeSignedUrls: false\n          #urlGeneratorResolver: Closure() {#2152\n            class: \"Illuminate\\Filesystem\\FilesystemManager\"\n            this: Illuminate\\Filesystem\\FilesystemManager {#1970 …}\n            file: \"C:\\laragon\\www\\remitify\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\FilesystemManager.php\"\n            line: \"193 to 193\"\n          }\n        }\n        #path: \"livewire-tmp/OrIHIDNVYKvAZrfrTpevntD5lJZYNm-metaaGVhZGVycy5wbmc=-.png\"\n        path: \"C:\\laragon\\www\\remitify\\storage\\app\\livewire-tmp\"\n        filename: \"OrIHIDNVYKvAZrfrTpevntD5lJZYNm-metaaGVhZGVycy5wbmc=-.png\"\n        basename: \"phpFC6E.tmp\"\n        pathname: \"C:\\laragon\\www\\remitify\\storage\\app\\livewire-tmp/OrIHIDNVYKvAZrfrTpevntD5lJZYNm-metaaGVhZGVycy5wbmc=-.png\"\n        extension: \"tmp\"\n        realPath: \"C:\\laragon\\www\\remitify\\storage\\app\\livewire-tmp/OrIHIDNVYKvAZrfrTpevntD5lJZYNm-metaaGVhZGVycy5wbmc=-.png\"\n        aTime: 2025-06-25 10:28:29\n        mTime: 2025-06-25 10:28:29\n        cTime: 2025-06-25 10:28:29\n        inode: 8162774324628760\n        size: 424086\n        writable: false\n        readable: false\n        executable: false\n        file: false\n        dir: false\n        link: false\n      }\n    ]\n  ]\n  \"name\" => \"agent.auth.register\"\n  \"component\" => \"App\\Livewire\\Agent\\Auth\\Register\"\n  \"id\" => \"l5Av3X5nDNyS4zjcQqSx\"\n]"}, "count": 1}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://remitify.test/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate", "uri": "POST livewire/update", "controller": "App\\Livewire\\Agent\\Auth\\Register@validateUploadedFile<a href=\"vscode://file/C%3A%2Flaragon%2Fwww%2Fremitify%2Fvendor%2Fspatie%2Flivewire-filepond%2Fsrc%2FWithFilePond.php:68\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"vscode://file/C%3A%2Flaragon%2Fwww%2Fremitify%2Fvendor%2Fspatie%2Flivewire-filepond%2Fsrc%2FWithFilePond.php:68\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/spatie/livewire-filepond/src/WithFilePond.php:68-71</a>", "middleware": "web", "duration": "595ms", "peak_memory": "34MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-621945706 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-621945706\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2022428844 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">RMxF3qX3ZYTEIfPd2KsBSsaXkg5FlaxjW4jANBbe</span>\"\n  \"<span class=sf-dump-key>components</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"559 characters\">{&quot;data&quot;:{&quot;first_name&quot;:null,&quot;last_name&quot;:null,&quot;email&quot;:null,&quot;country&quot;:&quot;Bangladesh:+880&quot;,&quot;gender&quot;:null,&quot;phone&quot;:null,&quot;username&quot;:null,&quot;password&quot;:null,&quot;password_confirmation&quot;:null,&quot;verification&quot;:[{&quot;2&quot;:[&quot;livewire-file:OrIHIDNVYKvAZrfrTpevntD5lJZYNm-metaaGVhZGVycy5wbmc=-.png&quot;,{&quot;s&quot;:&quot;fil&quot;}]},{&quot;s&quot;:&quot;arr&quot;}]},&quot;memo&quot;:{&quot;id&quot;:&quot;l5Av3X5nDNyS4zjcQqSx&quot;,&quot;name&quot;:&quot;agent.auth.register&quot;,&quot;path&quot;:&quot;agent\\/register&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[&quot;4169463714-0&quot;],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;40f8adf324d0682abc444cad793bdf707fc776afae74229ce68ca0cc13cbf2c6&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => []\n      \"<span class=sf-dump-key>calls</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"20 characters\">validateUploadedFile</span>\"\n          \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"56 characters\">OrIHIDNVYKvAZrfrTpevntD5lJZYNm-metaaGVhZGVycy5wbmc=-.png</span>\"\n          </samp>]\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2022428844\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-347246117 data-indent-pad=\"  \"><span class=sf-dump-note>array:12</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"714 characters\">XSRF-TOKEN=eyJpdiI6IlVCaHNpbnRTVVFoWkZHRFdmc3lETlE9PSIsInZhbHVlIjoiaW14eE5uNDhCQ1k5V2ErUU42bjg3cmNEc3ZoTEFybGZuczJQRTlWcit0eUc4cDdXRkJndVlDeUtzVTdCek90a3hFVDBjU2N2OEcrUkNFMlVGQmNBSjJpNlA3RmhOcE1SVEo3eG1xaTlreWJSTHNXSWNrZGQzdVVYek05a2VjcnkiLCJtYWMiOiI1NDAzODkyZTYxYmI4MjVkNWEzMzM1MDllYTNkMzZlZWUyYzE2Nzc2MTY0MWI4YzQwMzE3NDA4NTE4NzJlNWViIiwidGFnIjoiIn0%3D; remitify_session=eyJpdiI6IjR0SFVMaXh6Mkl4OTM1U2h2a3MyZ3c9PSIsInZhbHVlIjoiOEl3a2kwQy9qeHM0Um9OekdXZXc2NXAwelVCYjVBd2tWdXFwOUltTDZ1SXRMM1pzQ1JZOTBlUVJNS0p3UGxwTWh2d2cyNitubkw3QkZhd0Z4emFpTERJUFpRdzl5eE16Mm02c2pHS2xaL0VOSjh0MG9ySEdqcUVSV0J3Tzk4K0UiLCJtYWMiOiJiZmQwMGEwMzNjNDg2MmM1MzA5NDNkZDcxNTc0YTRkZTY3ODE0YWMyMDNjOWQxMThkNThiMzQ3NTM5NmQxYTY4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,bn;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">http://remitify.test/agent/register</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://remitify.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">853</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">close</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">remitify.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-347246117\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-857589934 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">RMxF3qX3ZYTEIfPd2KsBSsaXkg5FlaxjW4jANBbe</span>\"\n  \"<span class=sf-dump-key>remitify_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9zzkH57N3ClbDtxtBrJ1qDcWJzaLhV70SL6IRJQ6</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-857589934\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1318163757 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 25 Jun 2025 04:28:30 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1318163757\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1289820745 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">RMxF3qX3ZYTEIfPd2KsBSsaXkg5FlaxjW4jANBbe</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"35 characters\">http://remitify.test/agent/register</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1289820745\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://remitify.test/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate"}, "badge": null}}