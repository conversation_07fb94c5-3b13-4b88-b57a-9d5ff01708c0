{"__meta": {"id": "01JYJNGEHNFFS88N6QNPJV7SQ4", "datetime": "2025-06-25 10:28:32", "utime": **********.181823, "method": "POST", "uri": "/livewire/update", "ip": "127.0.0.1"}, "messages": {"count": 1, "messages": [{"message": "[10:28:31] LOG.warning: strip_tags(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\laragon\\www\\remitify\\app\\Http\\Middleware\\XSS.php on line 20", "message_html": null, "is_string": false, "label": "warning", "time": **********.808515, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.556474, "end": **********.181836, "duration": 0.6253619194030762, "duration_str": "625ms", "measures": [{"label": "Booting", "start": **********.556474, "relative_start": 0, "end": **********.780245, "relative_end": **********.780245, "duration": 0.****************, "duration_str": "224ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.780255, "relative_start": 0.*****************, "end": **********.181838, "relative_end": 2.1457672119140625e-06, "duration": 0.****************, "duration_str": "402ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.793352, "relative_start": 0.****************, "end": **********.795347, "relative_end": **********.795347, "duration": 0.001995086669921875, "duration_str": "2ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: frontend::agent.auth.forms.register", "start": **********.148398, "relative_start": 0.****************, "end": **********.148398, "relative_end": **********.148398, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-filepond::upload", "start": **********.1688, "relative_start": 0.****************, "end": **********.1688, "relative_end": **********.1688, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire-filepond::upload", "start": **********.177006, "relative_start": 0.****************, "end": **********.177006, "relative_end": **********.177006, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.179146, "relative_start": 0.6226720809936523, "end": **********.17994, "relative_end": **********.17994, "duration": 0.0007939338684082031, "duration_str": "794μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 35861048, "peak_usage_str": "34MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.19.3", "PHP Version": "8.3.13", "Environment": "local", "Debug Mode": "Enabled", "URL": "remitify.test", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 3, "nb_templates": 3, "templates": [{"name": "frontend::agent.auth.forms.register", "param_count": null, "params": [], "start": **********.148363, "type": "blade", "hash": "bladeC:\\laragon\\www\\remitify\\app\\Providers/../../resources/views/frontend/default/agent/auth/forms/register.blade.phpfrontend::agent.auth.forms.register", "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fremitify%2Fresources%2Fviews%2Ffrontend%2Fdefault%2Fagent%2Fauth%2Fforms%2Fregister.blade.php:1", "ajax": false, "filename": "register.blade.php", "line": "?"}}, {"name": "livewire-filepond::upload", "param_count": null, "params": [], "start": **********.168769, "type": "blade", "hash": "bladeC:\\laragon\\www\\remitify\\resources\\views/vendor/livewire-filepond/upload.blade.phplivewire-filepond::upload", "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fremitify%2Fresources%2Fviews%2Fvendor%2Flivewire-filepond%2Fupload.blade.php:1", "ajax": false, "filename": "upload.blade.php", "line": "?"}}, {"name": "livewire-filepond::upload", "param_count": null, "params": [], "start": **********.176977, "type": "blade", "hash": "bladeC:\\laragon\\www\\remitify\\resources\\views/vendor/livewire-filepond/upload.blade.phplivewire-filepond::upload", "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fremitify%2Fresources%2Fviews%2Fvendor%2Flivewire-filepond%2Fupload.blade.php:1", "ajax": false, "filename": "upload.blade.php", "line": "?"}}]}, "queries": {"count": 11, "nb_statements": 11, "nb_visible_statements": 11, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.0034500000000000004, "accumulated_duration_str": "3.45ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `sessions` where `id` = '9zzkH57N3ClbDtxtBrJ1qDcWJzaLhV70SL6IRJQ6' limit 1", "type": "query", "params": [], "bindings": ["9zzkH57N3ClbDtxtBrJ1qDcWJzaLhV70SL6IRJQ6"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\laragon\\www\\remitify\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\remitify\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\remitify\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\remitify\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\laragon\\www\\remitify\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.800084, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\laragon\\www\\remitify\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fremitify%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php:96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "remitify", "explain": null, "start_percent": 0, "width_percent": 20}, {"sql": "select * from `languages` where `is_default` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/helpers.php", "file": "C:\\laragon\\www\\remitify\\app\\helpers.php", "line": 366}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\remitify\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "C:\\laragon\\www\\remitify\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\remitify\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php", "file": "C:\\laragon\\www\\remitify\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php", "line": 87}], "start": **********.8039281, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "helpers.php:366", "source": {"index": 16, "namespace": null, "name": "app/helpers.php", "file": "C:\\laragon\\www\\remitify\\app\\helpers.php", "line": 366}, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fremitify%2Fapp%2Fhelpers.php:366", "ajax": false, "filename": "helpers.php", "line": "366"}, "connection": "remitify", "explain": null, "start_percent": 20, "width_percent": 12.754}, {"sql": "select * from `languages` where `status` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Providers/ViewServiceProvider.php", "file": "C:\\laragon\\www\\remitify\\app\\Providers\\ViewServiceProvider.php", "line": 62}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\laragon\\www\\remitify\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 162}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\laragon\\www\\remitify\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 178}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\laragon\\www\\remitify\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 189}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\laragon\\www\\remitify\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 160}], "start": **********.145088, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "ViewServiceProvider.php:62", "source": {"index": 15, "namespace": null, "name": "app/Providers/ViewServiceProvider.php", "file": "C:\\laragon\\www\\remitify\\app\\Providers\\ViewServiceProvider.php", "line": 62}, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fremitify%2Fapp%2FProviders%2FViewServiceProvider.php:62", "ajax": false, "filename": "ViewServiceProvider.php", "line": "62"}, "connection": "remitify", "explain": null, "start_percent": 32.754, "width_percent": 20}, {"sql": "select * from `page_settings` where `key` = 'agent_username_show' limit 1", "type": "query", "params": [], "bindings": ["agent_username_show"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/helpers.php", "file": "C:\\laragon\\www\\remitify\\app\\helpers.php", "line": 101}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\remitify\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\remitify\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\remitify\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\laragon\\www\\remitify\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.150283, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "helpers.php:101", "source": {"index": 16, "namespace": null, "name": "app/helpers.php", "file": "C:\\laragon\\www\\remitify\\app\\helpers.php", "line": 101}, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fremitify%2Fapp%2Fhelpers.php:101", "ajax": false, "filename": "helpers.php", "line": "101"}, "connection": "remitify", "explain": null, "start_percent": 52.754, "width_percent": 10.145}, {"sql": "select * from `page_settings` where `key` = 'agent_username_validation' limit 1", "type": "query", "params": [], "bindings": ["agent_username_validation"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/helpers.php", "file": "C:\\laragon\\www\\remitify\\app\\helpers.php", "line": 101}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\remitify\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\remitify\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\remitify\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\laragon\\www\\remitify\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.152108, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "helpers.php:101", "source": {"index": 16, "namespace": null, "name": "app/helpers.php", "file": "C:\\laragon\\www\\remitify\\app\\helpers.php", "line": 101}, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fremitify%2Fapp%2Fhelpers.php:101", "ajax": false, "filename": "helpers.php", "line": "101"}, "connection": "remitify", "explain": null, "start_percent": 62.899, "width_percent": 5.507}, {"sql": "select * from `page_settings` where `key` = 'agent_country_show' limit 1", "type": "query", "params": [], "bindings": ["agent_country_show"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/helpers.php", "file": "C:\\laragon\\www\\remitify\\app\\helpers.php", "line": 101}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\remitify\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\remitify\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\remitify\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\laragon\\www\\remitify\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.153166, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "helpers.php:101", "source": {"index": 16, "namespace": null, "name": "app/helpers.php", "file": "C:\\laragon\\www\\remitify\\app\\helpers.php", "line": 101}, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fremitify%2Fapp%2Fhelpers.php:101", "ajax": false, "filename": "helpers.php", "line": "101"}, "connection": "remitify", "explain": null, "start_percent": 68.406, "width_percent": 5.507}, {"sql": "select * from `page_settings` where `key` = 'agent_gender_show' limit 1", "type": "query", "params": [], "bindings": ["agent_gender_show"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/helpers.php", "file": "C:\\laragon\\www\\remitify\\app\\helpers.php", "line": 101}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\remitify\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\remitify\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\remitify\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\laragon\\www\\remitify\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.154174, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "helpers.php:101", "source": {"index": 16, "namespace": null, "name": "app/helpers.php", "file": "C:\\laragon\\www\\remitify\\app\\helpers.php", "line": 101}, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fremitify%2Fapp%2Fhelpers.php:101", "ajax": false, "filename": "helpers.php", "line": "101"}, "connection": "remitify", "explain": null, "start_percent": 73.913, "width_percent": 4.928}, {"sql": "select * from `page_settings` where `key` = 'agent_gender_validation' limit 1", "type": "query", "params": [], "bindings": ["agent_gender_validation"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/helpers.php", "file": "C:\\laragon\\www\\remitify\\app\\helpers.php", "line": 101}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\remitify\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\remitify\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\remitify\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\laragon\\www\\remitify\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.155252, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "helpers.php:101", "source": {"index": 16, "namespace": null, "name": "app/helpers.php", "file": "C:\\laragon\\www\\remitify\\app\\helpers.php", "line": 101}, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fremitify%2Fapp%2Fhelpers.php:101", "ajax": false, "filename": "helpers.php", "line": "101"}, "connection": "remitify", "explain": null, "start_percent": 78.841, "width_percent": 4.928}, {"sql": "select * from `page_settings` where `key` = 'agent_phone_show' limit 1", "type": "query", "params": [], "bindings": ["agent_phone_show"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/helpers.php", "file": "C:\\laragon\\www\\remitify\\app\\helpers.php", "line": 101}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\remitify\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\remitify\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\remitify\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\laragon\\www\\remitify\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.156377, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "helpers.php:101", "source": {"index": 16, "namespace": null, "name": "app/helpers.php", "file": "C:\\laragon\\www\\remitify\\app\\helpers.php", "line": 101}, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fremitify%2Fapp%2Fhelpers.php:101", "ajax": false, "filename": "helpers.php", "line": "101"}, "connection": "remitify", "explain": null, "start_percent": 83.768, "width_percent": 5.507}, {"sql": "select * from `page_settings` where `key` = 'agent_phone_validation' limit 1", "type": "query", "params": [], "bindings": ["agent_phone_validation"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/helpers.php", "file": "C:\\laragon\\www\\remitify\\app\\helpers.php", "line": 101}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\remitify\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\remitify\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\remitify\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\laragon\\www\\remitify\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.15744, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "helpers.php:101", "source": {"index": 16, "namespace": null, "name": "app/helpers.php", "file": "C:\\laragon\\www\\remitify\\app\\helpers.php", "line": 101}, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fremitify%2Fapp%2Fhelpers.php:101", "ajax": false, "filename": "helpers.php", "line": "101"}, "connection": "remitify", "explain": null, "start_percent": 89.275, "width_percent": 5.507}, {"sql": "select * from `agent_forms` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Livewire/Agent/Auth/Register.php", "file": "C:\\laragon\\www\\remitify\\app\\Livewire\\Agent\\Auth\\Register.php", "line": 30}, {"index": 20, "namespace": "view", "name": "frontend::agent.auth.forms.register", "file": "C:\\laragon\\www\\remitify\\app\\Providers/../../resources/views/frontend/default/agent/auth/forms/register.blade.php", "line": 239}, {"index": 22, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\remitify\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\remitify\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 74}, {"index": 24, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\laragon\\www\\remitify\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.158811, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "Register.php:30", "source": {"index": 19, "namespace": null, "name": "app/Livewire/Agent/Auth/Register.php", "file": "C:\\laragon\\www\\remitify\\app\\Livewire\\Agent\\Auth\\Register.php", "line": 30}, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fremitify%2Fapp%2FLivewire%2FAgent%2FAuth%2FRegister.php:30", "ajax": false, "filename": "Register.php", "line": "30"}, "connection": "remitify", "explain": null, "start_percent": 94.783, "width_percent": 5.217}]}, "models": {"data": {"App\\Models\\PageSetting": {"value": 7, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fremitify%2Fapp%2FModels%2FPageSetting.php:1", "ajax": false, "filename": "PageSetting.php", "line": "?"}}, "App\\Models\\Language": {"value": 1, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fremitify%2Fapp%2FModels%2FLanguage.php:1", "ajax": false, "filename": "Language.php", "line": "?"}}, "App\\Models\\AgentForm": {"value": 1, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fremitify%2Fapp%2FModels%2FAgentForm.php:1", "ajax": false, "filename": "AgentForm.php", "line": "?"}}}, "count": 9, "is_counter": true}, "livewire": {"data": {"agent.auth.register #l5Av3X5nDNyS4zjcQqSx": "array:4 [\n  \"data\" => array:10 [\n    \"first_name\" => null\n    \"last_name\" => null\n    \"email\" => null\n    \"country\" => \"Bangladesh:+880\"\n    \"gender\" => null\n    \"phone\" => null\n    \"username\" => null\n    \"password\" => null\n    \"password_confirmation\" => null\n    \"verification\" => array:1 [\n      2 => null\n    ]\n  ]\n  \"name\" => \"agent.auth.register\"\n  \"component\" => \"App\\Livewire\\Agent\\Auth\\Register\"\n  \"id\" => \"l5Av3X5nDNyS4zjcQqSx\"\n]"}, "count": 1}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://remitify.test/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate", "uri": "POST livewire/update", "controller": "App\\Livewire\\Agent\\Auth\\Register@revert<a href=\"vscode://file/C%3A%2Flaragon%2Fwww%2Fremitify%2Fvendor%2Fspatie%2Flivewire-filepond%2Fsrc%2FWithFilePond.php:30\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"vscode://file/C%3A%2Flaragon%2Fwww%2Fremitify%2Fvendor%2Fspatie%2Flivewire-filepond%2Fsrc%2FWithFilePond.php:30\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/spatie/livewire-filepond/src/WithFilePond.php:30-63</a>", "middleware": "web", "duration": "626ms", "peak_memory": "36MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-492989402 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-492989402\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1533273645 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">RMxF3qX3ZYTEIfPd2KsBSsaXkg5FlaxjW4jANBbe</span>\"\n  \"<span class=sf-dump-key>components</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"559 characters\">{&quot;data&quot;:{&quot;first_name&quot;:null,&quot;last_name&quot;:null,&quot;email&quot;:null,&quot;country&quot;:&quot;Bangladesh:+880&quot;,&quot;gender&quot;:null,&quot;phone&quot;:null,&quot;username&quot;:null,&quot;password&quot;:null,&quot;password_confirmation&quot;:null,&quot;verification&quot;:[{&quot;2&quot;:[&quot;livewire-file:OrIHIDNVYKvAZrfrTpevntD5lJZYNm-metaaGVhZGVycy5wbmc=-.png&quot;,{&quot;s&quot;:&quot;fil&quot;}]},{&quot;s&quot;:&quot;arr&quot;}]},&quot;memo&quot;:{&quot;id&quot;:&quot;l5Av3X5nDNyS4zjcQqSx&quot;,&quot;name&quot;:&quot;agent.auth.register&quot;,&quot;path&quot;:&quot;agent\\/register&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[&quot;4169463714-0&quot;],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;40f8adf324d0682abc444cad793bdf707fc776afae74229ce68ca0cc13cbf2c6&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => []\n      \"<span class=sf-dump-key>calls</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"6 characters\">revert</span>\"\n          \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">verification.2</span>\"\n            <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"56 characters\">OrIHIDNVYKvAZrfrTpevntD5lJZYNm-metaaGVhZGVycy5wbmc=-.png</span>\"\n            <span class=sf-dump-index>2</span> => \"\"\n          </samp>]\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1533273645\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-635195638 data-indent-pad=\"  \"><span class=sf-dump-note>array:12</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"714 characters\">XSRF-TOKEN=eyJpdiI6IlA1Nm13WlVLWkNJK1hWazBlRlVTOEE9PSIsInZhbHVlIjoibmRpUjZzcGVLZ2lqdnhHTWQ2MnNwZ21ZUS9XZ2x4Sk1CR2huYmdseCtmZXRUYU1PWVFZVTNra2hJWGVhcGdiRE80SWUwZTBDS1c2d0pwUElPNzl4UkRhWml3N0Ywai9PZTBYNENoSnVvelprbURQU2xiTUUwL3dhbzA1dzl2RlAiLCJtYWMiOiJhNDZlZTEwNjA1MzMxYTE1MTdhM2UxN2JiMGU2ZTEyNTU1YzQ3YzdhZTk2ZGRkODMxMmJiMzE5MDNjMzgzZTA2IiwidGFnIjoiIn0%3D; remitify_session=eyJpdiI6IjNkMnlxTE42VzM4Q1FLVGQyMEJzdnc9PSIsInZhbHVlIjoiUE9uR0NLYWxUWHI5NjZxWnhtWHlBZEN4VjJRK1RCdEplejlhNzNacFgvSm5WYVl3TFFBV1FXMk1nMFZYelBmbWVtSEhOVmpIZW5VOXdWVFVLeENMZGMzVWZWOEs1MHFQUzNlaXlXdW43cC9UV1ZEa0QrWktJdTdYQzQwT3VOSzMiLCJtYWMiOiIxYTU0Zjk4MmVmODc1ZWEwOTk4MDMxZDY0Y2Q0MTIwYTRjMDA0MGI3MTQxNDRiYzA5MzMwMTc4MjlhYTZmNDVkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,bn;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">http://remitify.test/agent/register</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://remitify.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">861</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">close</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">remitify.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-635195638\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1399397693 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">RMxF3qX3ZYTEIfPd2KsBSsaXkg5FlaxjW4jANBbe</span>\"\n  \"<span class=sf-dump-key>remitify_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9zzkH57N3ClbDtxtBrJ1qDcWJzaLhV70SL6IRJQ6</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1399397693\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1349534380 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 25 Jun 2025 04:28:32 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1349534380\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1715972787 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">RMxF3qX3ZYTEIfPd2KsBSsaXkg5FlaxjW4jANBbe</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"35 characters\">http://remitify.test/agent/register</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1715972787\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://remitify.test/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate"}, "badge": null}}