{"__meta": {"id": "01JYJNRC8PDKT9GV23E8RSAC8G", "datetime": "2025-06-25 10:32:51", "utime": **********.990797, "method": "POST", "uri": "/livewire/upload-file?expires=**********&signature=a37bb1bbd48d1de3ea1146d4e7137b4efe383d0e2c4d509e60566258fd4e4b53", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.675833, "end": **********.990809, "duration": 0.3149759769439697, "duration_str": "315ms", "measures": [{"label": "Booting", "start": **********.675833, "relative_start": 0, "end": **********.900099, "relative_end": **********.900099, "duration": 0.*****************, "duration_str": "224ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.900109, "relative_start": 0.*****************, "end": **********.990811, "relative_end": 2.1457672119140625e-06, "duration": 0.*****************, "duration_str": "90.7ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.912466, "relative_start": 0.****************, "end": **********.914193, "relative_end": **********.914193, "duration": 0.0017268657684326172, "duration_str": "1.73ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.986842, "relative_start": 0.*****************, "end": **********.988952, "relative_end": **********.988952, "duration": 0.002110004425048828, "duration_str": "2.11ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "32MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.19.3", "PHP Version": "8.3.13", "Environment": "local", "Debug Mode": "Enabled", "URL": "remitify.test", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 2, "nb_statements": 2, "nb_visible_statements": 2, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.0012100000000000001, "accumulated_duration_str": "1.21ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `sessions` where `id` = '9zzkH57N3ClbDtxtBrJ1qDcWJzaLhV70SL6IRJQ6' limit 1", "type": "query", "params": [], "bindings": ["9zzkH57N3ClbDtxtBrJ1qDcWJzaLhV70SL6IRJQ6"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\laragon\\www\\remitify\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\remitify\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\remitify\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\remitify\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\laragon\\www\\remitify\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.9189591, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\laragon\\www\\remitify\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fremitify%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php:96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "remitify", "explain": null, "start_percent": 0, "width_percent": 52.893}, {"sql": "select * from `languages` where `is_default` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/helpers.php", "file": "C:\\laragon\\www\\remitify\\app\\helpers.php", "line": 366}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\remitify\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "C:\\laragon\\www\\remitify\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\remitify\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}, {"index": 21, "namespace": "middleware", "name": "throttle", "file": "C:\\laragon\\www\\remitify\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php", "line": 160}], "start": **********.937774, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "helpers.php:366", "source": {"index": 16, "namespace": null, "name": "app/helpers.php", "file": "C:\\laragon\\www\\remitify\\app\\helpers.php", "line": 366}, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fremitify%2Fapp%2Fhelpers.php:366", "ajax": false, "filename": "helpers.php", "line": "366"}, "connection": "remitify", "explain": null, "start_percent": 52.893, "width_percent": 47.107}]}, "models": {"data": [], "count": 0, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://remitify.test/livewire/upload-file?expires=**********&signature=a37bb1bbd48d1de3ea1146d4e7137...", "action_name": "livewire.upload-file", "controller_action": "Livewire\\Features\\SupportFileUploads\\FileUploadController@handle", "uri": "POST livewire/upload-file", "controller": "Livewire\\Features\\SupportFileUploads\\FileUploadController@handle<a href=\"vscode://file/C%3A%2Flaragon%2Fwww%2Fremitify%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FFeatures%2FSupportFileUploads%2FFileUploadController.php:22\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"vscode://file/C%3A%2Flaragon%2Fwww%2Fremitify%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FFeatures%2FSupportFileUploads%2FFileUploadController.php:22\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/livewire/livewire/src/Features/SupportFileUploads/FileUploadController.php:22-31</a>", "duration": "314ms", "peak_memory": "34MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-679181070 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>expires</span>\" => \"<span class=sf-dump-str title=\"10 characters\">**********</span>\"\n  \"<span class=sf-dump-key>signature</span>\" => \"<span class=sf-dump-str title=\"64 characters\">a37bb1bbd48d1de3ea1146d4e7137b4efe383d0e2c4d509e60566258fd4e4b53</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-679181070\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1919908707 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>expires</span>\" => \"<span class=sf-dump-str title=\"10 characters\">**********</span>\"\n  \"<span class=sf-dump-key>signature</span>\" => \"<span class=sf-dump-str title=\"64 characters\">a37bb1bbd48d1de3ea1146d4e7137b4efe383d0e2c4d509e60566258fd4e4b53</span>\"\n  \"<span class=sf-dump-key>files</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">&lt;p&gt;C:\\Windows\\Temp\\phpFAD6.tmp&lt;/p&gt;</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1919908707\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1723073473 data-indent-pad=\"  \"><span class=sf-dump-note>array:12</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"714 characters\">XSRF-TOKEN=eyJpdiI6Ijc5YjloSmRYMjJKdFUyZ2tKUE0xK0E9PSIsInZhbHVlIjoiVzFxWmFWblFnVmpEaUkweEo5TlFsZkRpc0t3Tm9PY3I4cllGMUQvVzdZK05Bb21IZDhCT3psV3dxTTZhNE5SOFNjaXR6THRndUJOcERFdUZmWW1OV1gyRmpuZWkxTmxwOFFtSm9RcEhZZERFa05hNlFJMWdMekhDeDU2YTduSWMiLCJtYWMiOiI4Y2QzN2RmZTcwYWIyY2M1ZDFiMDVhMDU1MDExY2I0OTU4YTgyMzBhN2M5ZmRhNGZiZmVlNzk4MzcxMmQyYjNhIiwidGFnIjoiIn0%3D; remitify_session=eyJpdiI6Ink0Tll6NTBsZjdWdHVrZG5iR3M5Tnc9PSIsInZhbHVlIjoiQnJwaEoxUVAwZ1FiWnhMemptbmxxWFhnWW8vYVJna1EwbUxiVXIwVklmSiszRGN0RmZiRHNKd284SjhmK01QSE95UjZqbjZNS1F4WkVqbk15U2NUSlFhWHRMQUd1Y2FERjZobXNicGJMSW8xY3hDWWxZS00yOGdSQTNYVXNWWTEiLCJtYWMiOiI3ZmU2Yzg4MzUxZTY0YzMxYmE1MDljY2ZhYzdiNjZiNGNlOTRhODkyY2I0ZGI0MzFlMmJlOGM1ZjY2NDhjYTAzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,bn;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">http://remitify.test/agent/register</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://remitify.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">multipart/form-data; boundary=----WebKitFormBoundaryKA1GxAXCc3E1H7vX</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">RMxF3qX3ZYTEIfPd2KsBSsaXkg5FlaxjW4jANBbe</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">2460</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">close</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">remitify.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1723073473\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-726446214 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">RMxF3qX3ZYTEIfPd2KsBSsaXkg5FlaxjW4jANBbe</span>\"\n  \"<span class=sf-dump-key>remitify_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9zzkH57N3ClbDtxtBrJ1qDcWJzaLhV70SL6IRJQ6</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-726446214\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1043781069 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 25 Jun 2025 04:32:51 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1043781069\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1938112399 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">RMxF3qX3ZYTEIfPd2KsBSsaXkg5FlaxjW4jANBbe</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"35 characters\">http://remitify.test/agent/register</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1938112399\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://remitify.test/livewire/upload-file?expires=**********&signature=a37bb1bbd48d1de3ea1146d4e7137...", "action_name": "livewire.upload-file", "controller_action": "Livewire\\Features\\SupportFileUploads\\FileUploadController@handle"}, "badge": null}}