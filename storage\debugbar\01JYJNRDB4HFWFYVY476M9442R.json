{"__meta": {"id": "01JYJNRDB4HFWFYVY476M9442R", "datetime": "2025-06-25 10:32:53", "utime": **********.092749, "method": "POST", "uri": "/livewire/update", "ip": "127.0.0.1"}, "messages": {"count": 1, "messages": [{"message": "[10:32:52] LOG.error: Property [$files[]] not found on component: [agent.auth.register] {\n    \"exception\": {}\n}", "message_html": null, "is_string": false, "label": "error", "time": **********.291469, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.010486, "end": **********.092763, "duration": 1.0822770595550537, "duration_str": "1.08s", "measures": [{"label": "Booting", "start": **********.010486, "relative_start": 0, "end": **********.228172, "relative_end": **********.228172, "duration": 0.*****************, "duration_str": "218ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.228179, "relative_start": 0.*****************, "end": **********.092766, "relative_end": 3.0994415283203125e-06, "duration": 0.****************, "duration_str": "865ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.239681, "relative_start": 0.*****************, "end": **********.241268, "relative_end": **********.241268, "duration": 0.*************, "duration_str": "1.59ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: laravel-exceptions-renderer::show", "start": **********.389165, "relative_start": 0.****************, "end": **********.389165, "relative_end": **********.389165, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.navigation", "start": **********.397575, "relative_start": 0.****************, "end": **********.397575, "relative_end": **********.397575, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.theme-switcher", "start": **********.405094, "relative_start": 0.3946080207824707, "end": **********.405094, "relative_end": **********.405094, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.icons.sun", "start": **********.412424, "relative_start": 0.40193819999694824, "end": **********.412424, "relative_end": **********.412424, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.icons.moon", "start": **********.419635, "relative_start": 0.409149169921875, "end": **********.419635, "relative_end": **********.419635, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.icons.sun", "start": **********.426504, "relative_start": 0.416018009185791, "end": **********.426504, "relative_end": **********.426504, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.icons.moon", "start": **********.433347, "relative_start": 0.42286109924316406, "end": **********.433347, "relative_end": **********.433347, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.icons.computer-desktop", "start": **********.440188, "relative_start": 0.4297020435333252, "end": **********.440188, "relative_end": **********.440188, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.header", "start": **********.447157, "relative_start": 0.*****************, "end": **********.447157, "relative_end": **********.447157, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.card", "start": **********.454135, "relative_start": 0.****************, "end": **********.454135, "relative_end": **********.454135, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.trace-and-editor", "start": **********.461199, "relative_start": 0.****************, "end": **********.461199, "relative_end": **********.461199, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.trace", "start": **********.95524, "relative_start": 0.9447541236877441, "end": **********.95524, "relative_end": **********.95524, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.icons.chevron-down", "start": **********.962703, "relative_start": 0.9522171020507812, "end": **********.962703, "relative_end": **********.962703, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.icons.chevron-up", "start": **********.969969, "relative_start": 0.9594831466674805, "end": **********.969969, "relative_end": **********.969969, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.icons.chevron-up", "start": **********.976869, "relative_start": 0.9663832187652588, "end": **********.976869, "relative_end": **********.976869, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.icons.chevron-down", "start": **********.983681, "relative_start": 0.9731950759887695, "end": **********.983681, "relative_end": **********.983681, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.editor", "start": **********.007776, "relative_start": 0.9972901344299316, "end": **********.007776, "relative_end": **********.007776, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.card", "start": **********.053065, "relative_start": 1.042579174041748, "end": **********.053065, "relative_end": **********.053065, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.context", "start": **********.060188, "relative_start": 1.0497021675109863, "end": **********.060188, "relative_end": **********.060188, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.card", "start": **********.067367, "relative_start": 1.0568811893463135, "end": **********.067367, "relative_end": **********.067367, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.card", "start": **********.07424, "relative_start": 1.0637540817260742, "end": **********.07424, "relative_end": **********.07424, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-exceptions-renderer::components.layout", "start": **********.081315, "relative_start": 1.070829153060913, "end": **********.081315, "relative_end": **********.081315, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}]}, "memory": {"peak_usage": 42472136, "peak_usage_str": "41MB"}, "exceptions": {"count": 1, "exceptions": [{"type": "Livewire\\Exceptions\\PropertyNotFoundException", "message": "Property [$files[]] not found on component: [agent.auth.register]", "code": 0, "file": "vendor/livewire/livewire/src/Component.php", "line": 105, "stack_trace": null, "stack_trace_html": "<pre class=sf-dump id=sf-dump-1143073148 data-indent-pad=\"  \"><span class=sf-dump-note>array:73</span> [<samp data-depth=1 class=sf-dump-expanded>\n  <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"65 characters\">vendor/livewire/livewire/src/Concerns/InteractsWithProperties.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>17</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"5 characters\">__get</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Livewire\\Component</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">files[]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"76 characters\">vendor/livewire/livewire/src/Features/SupportFileUploads/WithFileUploads.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>45</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"16 characters\">getPropertyValue</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Livewire\\Component</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">files[]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"65 characters\">vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>36</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"13 characters\">_finishUpload</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"32 characters\">App\\Livewire\\Agent\\Auth\\Register</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">files[]</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"69 characters\">/xWUIw1MUvnn5CnhPkF5MlQew3j01Jn-metaMDQgLSBDb3B5IC0gQ29weS5wbmc=-.png</span>\"\n      </samp>]\n      <span class=sf-dump-index>2</span> => \"\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Container/Util.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>43</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"30 characters\">Illuminate\\Container\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Illuminate\\Container\\BoundMethod</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">::</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"65 characters\">vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>96</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"15 characters\">unwrapIfClosure</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Container\\Util</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">::</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"65 characters\">vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>35</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"15 characters\">callBoundMethod</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Illuminate\\Container\\BoundMethod</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">::</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"42 characters\">[object Illuminate\\Foundation\\Application]</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Livewire\\Agent\\Auth\\Register\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Livewire\\Agent\\Auth</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Register</span></span> {<a class=sf-dump-ref href=#sf-dump-1143073148-ref22134 title=\"4 occurrences\">#2134</a><samp data-depth=5 id=sf-dump-1143073148-ref22134 class=sf-dump-compact>\n          #<span class=sf-dump-protected title=\"Protected property\">__id</span>: \"<span class=sf-dump-str title=\"20 characters\">CNiadgtC0wt2iYBTAeyS</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">__name</span>: \"<span class=sf-dump-str title=\"19 characters\">agent.auth.register</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">listeners</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Livewire\\Features\\SupportAttributes\\AttributeCollection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Livewire\\Features\\SupportAttributes</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">AttributeCollection</span></span> {<a class=sf-dump-ref>#2260</a><samp data-depth=6 class=sf-dump-compact>\n            #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Livewire\\Attributes\\Computed\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Livewire\\Attributes</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Computed</span></span> {<a class=sf-dump-ref>#2336</a><samp data-depth=8 class=sf-dump-compact>\n                #<span class=sf-dump-protected title=\"Protected property\">component</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Livewire\\Agent\\Auth\\Register\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Livewire\\Agent\\Auth</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Register</span></span> {<a class=sf-dump-ref href=#sf-dump-1143073148-ref22134 title=\"4 occurrences\">#2134</a>}\n                #<span class=sf-dump-protected title=\"Protected property\">subTarget</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">subName</span>: \"<span class=sf-dump-str title=\"19 characters\">getVerificationInfo</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">level</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Livewire\\Features\\SupportAttributes\\AttributeLevel\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Livewire\\Features\\SupportAttributes</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">AttributeLevel</span></span> {<a class=sf-dump-ref href=#sf-dump-1143073148-ref22338 title=\"2 occurrences\">#2338</a><samp data-depth=9 id=sf-dump-1143073148-ref22338 class=sf-dump-compact>\n                  +<span class=sf-dump-public title=\"Public property\">name</span>: \"<span class=sf-dump-str title=\"6 characters\">METHOD</span>\"\n                </samp>}\n                #<span class=sf-dump-protected title=\"Protected property\">levelName</span>: \"<span class=sf-dump-str title=\"19 characters\">getVerificationInfo</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">requestCachedValue</span>: <span class=sf-dump-const>null</span>\n                +<span class=sf-dump-public title=\"Public property\">persist</span>: <span class=sf-dump-const>false</span>\n                +<span class=sf-dump-public title=\"Public property\">seconds</span>: <span class=sf-dump-num>3600</span>\n                +<span class=sf-dump-public title=\"Public property\">cache</span>: <span class=sf-dump-const>false</span>\n                +<span class=sf-dump-public title=\"Public property\">key</span>: <span class=sf-dump-const>null</span>\n                +<span class=sf-dump-public title=\"Public property\">tags</span>: <span class=sf-dump-const>null</span>\n              </samp>}\n              <span class=sf-dump-index>1</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Livewire\\Attributes\\Renderless\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Livewire\\Attributes</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Renderless</span></span> {<a class=sf-dump-ref>#2335</a><samp data-depth=8 class=sf-dump-compact>\n                #<span class=sf-dump-protected title=\"Protected property\">component</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Livewire\\Agent\\Auth\\Register\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Livewire\\Agent\\Auth</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Register</span></span> {<a class=sf-dump-ref href=#sf-dump-1143073148-ref22134 title=\"4 occurrences\">#2134</a>}\n                #<span class=sf-dump-protected title=\"Protected property\">subTarget</span>: <span class=sf-dump-const>null</span>\n                #<span class=sf-dump-protected title=\"Protected property\">subName</span>: \"<span class=sf-dump-str title=\"12 characters\">_startUpload</span>\"\n                #<span class=sf-dump-protected title=\"Protected property\">level</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Livewire\\Features\\SupportAttributes\\AttributeLevel\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Livewire\\Features\\SupportAttributes</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">AttributeLevel</span></span> {<a class=sf-dump-ref href=#sf-dump-1143073148-ref22338 title=\"2 occurrences\">#2338</a>}\n                #<span class=sf-dump-protected title=\"Protected property\">levelName</span>: \"<span class=sf-dump-str title=\"12 characters\">_startUpload</span>\"\n              </samp>}\n            </samp>]\n            #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n          </samp>}\n          #<span class=sf-dump-protected title=\"Protected property\">withValidatorCallback</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">rulesFromOutside</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">messagesFromOutside</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">validationAttributesFromOutside</span>: []\n          +<span class=sf-dump-public title=\"Public property\">first_name</span>: <span class=sf-dump-const>null</span>\n          +<span class=sf-dump-public title=\"Public property\">last_name</span>: <span class=sf-dump-const>null</span>\n          +<span class=sf-dump-public title=\"Public property\">email</span>: <span class=sf-dump-const>null</span>\n          +<span class=sf-dump-public title=\"Public property\">country</span>: \"<span class=sf-dump-str title=\"15 characters\">Bangladesh:+880</span>\"\n          +<span class=sf-dump-public title=\"Public property\">gender</span>: <span class=sf-dump-const>null</span>\n          +<span class=sf-dump-public title=\"Public property\">phone</span>: <span class=sf-dump-const>null</span>\n          +<span class=sf-dump-public title=\"Public property\">username</span>: <span class=sf-dump-const>null</span>\n          +<span class=sf-dump-public title=\"Public property\">password</span>: <span class=sf-dump-const>null</span>\n          +<span class=sf-dump-public title=\"Public property\">password_confirmation</span>: <span class=sf-dump-const>null</span>\n          +<span class=sf-dump-public title=\"Public property\">verification</span>: []\n        </samp>}\n        <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"13 characters\">_finishUpload</span>\"\n      </samp>]\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"40 characters\">vendor/livewire/livewire/src/Wrapped.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>23</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">call</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Illuminate\\Container\\BoundMethod</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">::</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"42 characters\">[object Illuminate\\Foundation\\Application]</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Livewire\\Agent\\Auth\\Register\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Livewire\\Agent\\Auth</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Register</span></span> {<a class=sf-dump-ref href=#sf-dump-1143073148-ref22134 title=\"4 occurrences\">#2134</a>}\n        <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"13 characters\">_finishUpload</span>\"\n      </samp>]\n      <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">files[]</span>\"\n        <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"69 characters\">/xWUIw1MUvnn5CnhPkF5MlQew3j01Jn-metaMDQgLSBDb3B5IC0gQ29weS5wbmc=-.png</span>\"\n        </samp>]\n        <span class=sf-dump-index>2</span> => \"\"\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>7</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"77 characters\">vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>474</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">__call</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Livewire\\Wrapped</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">_finishUpload</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">files[]</span>\"\n        <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"69 characters\">/xWUIw1MUvnn5CnhPkF5MlQew3j01Jn-metaMDQgLSBDb3B5IC0gQ29weS5wbmc=-.png</span>\"\n        </samp>]\n        <span class=sf-dump-index>2</span> => \"\"\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>8</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"77 characters\">vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>101</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"11 characters\">callMethods</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"53 characters\">Livewire\\Mechanisms\\HandleComponents\\HandleComponents</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">[object App\\Livewire\\Agent\\Auth\\Register]</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"13 characters\">_finishUpload</span>\"\n          \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">files[]</span>\"\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"69 characters\">/xWUIw1MUvnn5CnhPkF5MlQew3j01Jn-metaMDQgLSBDb3B5IC0gQ29weS5wbmc=-.png</span>\"\n            </samp>]\n            <span class=sf-dump-index>2</span> => \"\"\n          </samp>]\n        </samp>]\n      </samp>]\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"62 characters\">[object Livewire\\Mechanisms\\HandleComponents\\ComponentContext]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>9</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"48 characters\">vendor/livewire/livewire/src/LivewireManager.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>102</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"53 characters\">Livewire\\Mechanisms\\HandleComponents\\HandleComponents</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>data</span>\" => <span class=sf-dump-note>array:10</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>first_name</span>\" => <span class=sf-dump-const>null</span>\n          \"<span class=sf-dump-key>last_name</span>\" => <span class=sf-dump-const>null</span>\n          \"<span class=sf-dump-key>email</span>\" => <span class=sf-dump-const>null</span>\n          \"<span class=sf-dump-key>country</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Bangladesh:+880</span>\"\n          \"<span class=sf-dump-key>gender</span>\" => <span class=sf-dump-const>null</span>\n          \"<span class=sf-dump-key>phone</span>\" => <span class=sf-dump-const>null</span>\n          \"<span class=sf-dump-key>username</span>\" => <span class=sf-dump-const>null</span>\n          \"<span class=sf-dump-key>password</span>\" => <span class=sf-dump-const>null</span>\n          \"<span class=sf-dump-key>password_confirmation</span>\" => <span class=sf-dump-const>null</span>\n          \"<span class=sf-dump-key>verification</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => []\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>s</span>\" => \"<span class=sf-dump-str title=\"3 characters\">arr</span>\"\n            </samp>]\n          </samp>]\n        </samp>]\n        \"<span class=sf-dump-key>memo</span>\" => <span class=sf-dump-note>array:9</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"20 characters\">CNiadgtC0wt2iYBTAeyS</span>\"\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"19 characters\">agent.auth.register</span>\"\n          \"<span class=sf-dump-key>path</span>\" => \"<span class=sf-dump-str title=\"14 characters\">agent/register</span>\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n          \"<span class=sf-dump-key>children</span>\" => []\n          \"<span class=sf-dump-key>scripts</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">4169463714-0</span>\"\n          </samp>]\n          \"<span class=sf-dump-key>assets</span>\" => []\n          \"<span class=sf-dump-key>errors</span>\" => []\n          \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>checksum</span>\" => \"<span class=sf-dump-str title=\"64 characters\">c85e7c1ee0f9ce7a163d9a1e1f5f76cf22f562a053953c597a5242fd80f5913f</span>\"\n      </samp>]\n      <span class=sf-dump-index>1</span> => []\n      <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"13 characters\">_finishUpload</span>\"\n          \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">files[]</span>\"\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"69 characters\">/xWUIw1MUvnn5CnhPkF5MlQew3j01Jn-metaMDQgLSBDb3B5IC0gQ29weS5wbmc=-.png</span>\"\n            </samp>]\n            <span class=sf-dump-index>2</span> => \"\"\n          </samp>]\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>10</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"73 characters\">vendor/livewire/livewire/src/Mechanisms/HandleRequests/HandleRequests.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>94</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Livewire\\LivewireManager</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>data</span>\" => <span class=sf-dump-note>array:10</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>first_name</span>\" => <span class=sf-dump-const>null</span>\n          \"<span class=sf-dump-key>last_name</span>\" => <span class=sf-dump-const>null</span>\n          \"<span class=sf-dump-key>email</span>\" => <span class=sf-dump-const>null</span>\n          \"<span class=sf-dump-key>country</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Bangladesh:+880</span>\"\n          \"<span class=sf-dump-key>gender</span>\" => <span class=sf-dump-const>null</span>\n          \"<span class=sf-dump-key>phone</span>\" => <span class=sf-dump-const>null</span>\n          \"<span class=sf-dump-key>username</span>\" => <span class=sf-dump-const>null</span>\n          \"<span class=sf-dump-key>password</span>\" => <span class=sf-dump-const>null</span>\n          \"<span class=sf-dump-key>password_confirmation</span>\" => <span class=sf-dump-const>null</span>\n          \"<span class=sf-dump-key>verification</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => []\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>s</span>\" => \"<span class=sf-dump-str title=\"3 characters\">arr</span>\"\n            </samp>]\n          </samp>]\n        </samp>]\n        \"<span class=sf-dump-key>memo</span>\" => <span class=sf-dump-note>array:9</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"20 characters\">CNiadgtC0wt2iYBTAeyS</span>\"\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"19 characters\">agent.auth.register</span>\"\n          \"<span class=sf-dump-key>path</span>\" => \"<span class=sf-dump-str title=\"14 characters\">agent/register</span>\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n          \"<span class=sf-dump-key>children</span>\" => []\n          \"<span class=sf-dump-key>scripts</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">4169463714-0</span>\"\n          </samp>]\n          \"<span class=sf-dump-key>assets</span>\" => []\n          \"<span class=sf-dump-key>errors</span>\" => []\n          \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>checksum</span>\" => \"<span class=sf-dump-str title=\"64 characters\">c85e7c1ee0f9ce7a163d9a1e1f5f76cf22f562a053953c597a5242fd80f5913f</span>\"\n      </samp>]\n      <span class=sf-dump-index>1</span> => []\n      <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"13 characters\">_finishUpload</span>\"\n          \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">files[]</span>\"\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"69 characters\">/xWUIw1MUvnn5CnhPkF5MlQew3j01Jn-metaMDQgLSBDb3B5IC0gQ29weS5wbmc=-.png</span>\"\n            </samp>]\n            <span class=sf-dump-index>2</span> => \"\"\n          </samp>]\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>11</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"72 characters\">vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>46</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"12 characters\">handleUpdate</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"49 characters\">Livewire\\Mechanisms\\HandleRequests\\HandleRequests</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>12</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"57 characters\">vendor/laravel/framework/src/Illuminate/Routing/Route.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>265</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"8 characters\">dispatch</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"39 characters\">Illuminate\\Routing\\ControllerDispatcher</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">[object Illuminate\\Routing\\Route]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"58 characters\">[object Livewire\\Mechanisms\\HandleRequests\\HandleRequests]</span>\"\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"12 characters\">handleUpdate</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>13</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"57 characters\">vendor/laravel/framework/src/Illuminate/Routing/Route.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>211</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"13 characters\">runController</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Illuminate\\Routing\\Route</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>14</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>808</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"3 characters\">run</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Illuminate\\Routing\\Route</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>15</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>169</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Routing\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>16</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>66</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>17</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Barryvdh\\Debugbar\\Middleware\\InjectDebugbar</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>18</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"37 characters\">app/Http/Middleware/IsMaintenance.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>37</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>19</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">App\\Http\\Middleware\\IsMaintenance</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>20</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"27 characters\">app/Http/Middleware/XSS.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>25</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>21</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"23 characters\">App\\Http\\Middleware\\XSS</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>22</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"57 characters\">modules/installer/src/Http/Middleware/ValidateLicense.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>19</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>23</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"54 characters\">Remotelywork\\Installer\\Http\\Middleware\\ValidateLicense</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>24</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"54 characters\">modules/installer/src/Http/Middleware/InstallCheck.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>17</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>25</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"51 characters\">Remotelywork\\Installer\\Http\\Middleware\\InstallCheck</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>26</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"36 characters\">app/Http/Middleware/Localization.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>21</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>27</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"32 characters\">App\\Http\\Middleware\\Localization</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>28</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"81 characters\">vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>50</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>29</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"48 characters\">Illuminate\\Routing\\Middleware\\SubstituteBindings</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>30</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"86 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>87</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>31</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"53 characters\">Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>32</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"82 characters\">vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>48</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>33</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"49 characters\">Illuminate\\View\\Middleware\\ShareErrorsFromSession</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>34</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"75 characters\">vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>120</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>35</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"75 characters\">vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>63</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"21 characters\">handleStatefulRequest</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"42 characters\">Illuminate\\Session\\Middleware\\StartSession</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"33 characters\">[object Illuminate\\Session\\Store]</span>\"\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>36</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"42 characters\">Illuminate\\Session\\Middleware\\StartSession</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>37</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"88 characters\">vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>36</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>38</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"55 characters\">Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>39</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"76 characters\">vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>74</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>40</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Illuminate\\Cookie\\Middleware\\EncryptCookies</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>41</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>126</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>42</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>807</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">then</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>43</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>786</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"19 characters\">runRouteWithinStack</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">[object Illuminate\\Routing\\Route]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>44</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>750</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"8 characters\">runRoute</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"33 characters\">[object Illuminate\\Routing\\Route]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>45</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>739</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"15 characters\">dispatchToRoute</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>46</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>200</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"8 characters\">dispatch</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>47</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>169</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"36 characters\">Illuminate\\Foundation\\Http\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>48</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"106 characters\">vendor/livewire/livewire/src/Features/SupportDisablingBackButtonCache/DisableBackButtonCacheMiddleware.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>19</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>49</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"82 characters\">Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>50</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>66</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>51</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Barryvdh\\Debugbar\\Middleware\\InjectDebugbar</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>52</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"96 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>27</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>53</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"63 characters\">Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>54</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"82 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>47</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>55</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"49 characters\">Illuminate\\Foundation\\Http\\Middleware\\TrimStrings</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>56</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"76 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePostSize.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>27</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>57</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Illuminate\\Http\\Middleware\\ValidatePostSize</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>58</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"103 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>109</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>59</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"70 characters\">Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>60</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"70 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>48</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>61</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"37 characters\">Illuminate\\Http\\Middleware\\HandleCors</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>62</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"72 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>58</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>63</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"39 characters\">Illuminate\\Http\\Middleware\\TrustProxies</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>64</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"94 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/InvokeDeferredCallbacks.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>22</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>65</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"61 characters\">Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>66</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"80 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePathEncoding.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>26</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>67</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>208</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"47 characters\">Illuminate\\Http\\Middleware\\ValidatePathEncoding</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>68</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>126</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>69</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>175</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">then</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>70</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>144</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"24 characters\">sendRequestThroughRouter</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>71</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Application.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>1219</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>72</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"9 characters\">index.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>17</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"13 characters\">handleRequest</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Application</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1143073148\", {\"maxDepth\":0})</script>\n", "surrounding_lines": ["        $value = $finish($value);\n", "\n", "        if ($value === 'noneset') {\n", "            throw new PropertyNotFoundException($property, $this->getName());\n", "        }\n", "\n", "        return $value;\n"], "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fremitify%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FComponent.php:105", "ajax": false, "filename": "Component.php", "line": "105"}}]}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.19.3", "PHP Version": "8.3.13", "Environment": "local", "Debug Mode": "Enabled", "URL": "remitify.test", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 22, "nb_templates": 22, "templates": [{"name": "laravel-exceptions-renderer::show", "param_count": null, "params": [], "start": **********.389131, "type": "blade", "hash": "bladeC:\\laragon\\www\\remitify\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/show.blade.phplaravel-exceptions-renderer::show", "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fremitify%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fshow.blade.php:1", "ajax": false, "filename": "show.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.navigation", "param_count": null, "params": [], "start": **********.39754, "type": "blade", "hash": "bladeC:\\laragon\\www\\remitify\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/navigation.blade.phplaravel-exceptions-renderer::components.navigation", "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fremitify%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Fnavigation.blade.php:1", "ajax": false, "filename": "navigation.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.theme-switcher", "param_count": null, "params": [], "start": **********.405061, "type": "blade", "hash": "bladeC:\\laragon\\www\\remitify\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/theme-switcher.blade.phplaravel-exceptions-renderer::components.theme-switcher", "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fremitify%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Ftheme-switcher.blade.php:1", "ajax": false, "filename": "theme-switcher.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.icons.sun", "param_count": null, "params": [], "start": **********.412394, "type": "blade", "hash": "bladeC:\\laragon\\www\\remitify\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/icons/sun.blade.phplaravel-exceptions-renderer::components.icons.sun", "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fremitify%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Ficons%2Fsun.blade.php:1", "ajax": false, "filename": "sun.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.icons.moon", "param_count": null, "params": [], "start": **********.419605, "type": "blade", "hash": "bladeC:\\laragon\\www\\remitify\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/icons/moon.blade.phplaravel-exceptions-renderer::components.icons.moon", "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fremitify%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Ficons%2Fmoon.blade.php:1", "ajax": false, "filename": "moon.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.icons.sun", "param_count": null, "params": [], "start": **********.426478, "type": "blade", "hash": "bladeC:\\laragon\\www\\remitify\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/icons/sun.blade.phplaravel-exceptions-renderer::components.icons.sun", "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fremitify%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Ficons%2Fsun.blade.php:1", "ajax": false, "filename": "sun.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.icons.moon", "param_count": null, "params": [], "start": **********.433319, "type": "blade", "hash": "bladeC:\\laragon\\www\\remitify\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/icons/moon.blade.phplaravel-exceptions-renderer::components.icons.moon", "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fremitify%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Ficons%2Fmoon.blade.php:1", "ajax": false, "filename": "moon.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.icons.computer-desktop", "param_count": null, "params": [], "start": **********.440158, "type": "blade", "hash": "bladeC:\\laragon\\www\\remitify\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/icons/computer-desktop.blade.phplaravel-exceptions-renderer::components.icons.computer-desktop", "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fremitify%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Ficons%2Fcomputer-desktop.blade.php:1", "ajax": false, "filename": "computer-desktop.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.header", "param_count": null, "params": [], "start": **********.447119, "type": "blade", "hash": "bladeC:\\laragon\\www\\remitify\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/header.blade.phplaravel-exceptions-renderer::components.header", "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fremitify%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Fheader.blade.php:1", "ajax": false, "filename": "header.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.card", "param_count": null, "params": [], "start": **********.454107, "type": "blade", "hash": "bladeC:\\laragon\\www\\remitify\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/card.blade.phplaravel-exceptions-renderer::components.card", "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fremitify%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Fcard.blade.php:1", "ajax": false, "filename": "card.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.trace-and-editor", "param_count": null, "params": [], "start": **********.46117, "type": "blade", "hash": "bladeC:\\laragon\\www\\remitify\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/trace-and-editor.blade.phplaravel-exceptions-renderer::components.trace-and-editor", "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fremitify%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Ftrace-and-editor.blade.php:1", "ajax": false, "filename": "trace-and-editor.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.trace", "param_count": null, "params": [], "start": **********.955207, "type": "blade", "hash": "bladeC:\\laragon\\www\\remitify\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/trace.blade.phplaravel-exceptions-renderer::components.trace", "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fremitify%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Ftrace.blade.php:1", "ajax": false, "filename": "trace.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.icons.chevron-down", "param_count": null, "params": [], "start": **********.962672, "type": "blade", "hash": "bladeC:\\laragon\\www\\remitify\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/icons/chevron-down.blade.phplaravel-exceptions-renderer::components.icons.chevron-down", "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fremitify%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Ficons%2Fchevron-down.blade.php:1", "ajax": false, "filename": "chevron-down.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.icons.chevron-up", "param_count": null, "params": [], "start": **********.969939, "type": "blade", "hash": "bladeC:\\laragon\\www\\remitify\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/icons/chevron-up.blade.phplaravel-exceptions-renderer::components.icons.chevron-up", "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fremitify%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Ficons%2Fchevron-up.blade.php:1", "ajax": false, "filename": "chevron-up.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.icons.chevron-up", "param_count": null, "params": [], "start": **********.976842, "type": "blade", "hash": "bladeC:\\laragon\\www\\remitify\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/icons/chevron-up.blade.phplaravel-exceptions-renderer::components.icons.chevron-up", "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fremitify%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Ficons%2Fchevron-up.blade.php:1", "ajax": false, "filename": "chevron-up.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.icons.chevron-down", "param_count": null, "params": [], "start": **********.983654, "type": "blade", "hash": "bladeC:\\laragon\\www\\remitify\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/icons/chevron-down.blade.phplaravel-exceptions-renderer::components.icons.chevron-down", "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fremitify%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Ficons%2Fchevron-down.blade.php:1", "ajax": false, "filename": "chevron-down.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.editor", "param_count": null, "params": [], "start": **********.007742, "type": "blade", "hash": "bladeC:\\laragon\\www\\remitify\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/editor.blade.phplaravel-exceptions-renderer::components.editor", "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fremitify%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Feditor.blade.php:1", "ajax": false, "filename": "editor.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.card", "param_count": null, "params": [], "start": **********.053035, "type": "blade", "hash": "bladeC:\\laragon\\www\\remitify\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/card.blade.phplaravel-exceptions-renderer::components.card", "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fremitify%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Fcard.blade.php:1", "ajax": false, "filename": "card.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.context", "param_count": null, "params": [], "start": **********.060146, "type": "blade", "hash": "bladeC:\\laragon\\www\\remitify\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/context.blade.phplaravel-exceptions-renderer::components.context", "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fremitify%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Fcontext.blade.php:1", "ajax": false, "filename": "context.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.card", "param_count": null, "params": [], "start": **********.06734, "type": "blade", "hash": "bladeC:\\laragon\\www\\remitify\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/card.blade.phplaravel-exceptions-renderer::components.card", "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fremitify%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Fcard.blade.php:1", "ajax": false, "filename": "card.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.card", "param_count": null, "params": [], "start": **********.074214, "type": "blade", "hash": "bladeC:\\laragon\\www\\remitify\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/card.blade.phplaravel-exceptions-renderer::components.card", "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fremitify%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Fcard.blade.php:1", "ajax": false, "filename": "card.blade.php", "line": "?"}}, {"name": "laravel-exceptions-renderer::components.layout", "param_count": null, "params": [], "start": **********.081286, "type": "blade", "hash": "bladeC:\\laragon\\www\\remitify\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers/../resources/exceptions/renderer/components/layout.blade.phplaravel-exceptions-renderer::components.layout", "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fremitify%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2Fresources%2Fexceptions%2Frenderer%2Fcomponents%2Flayout.blade.php:1", "ajax": false, "filename": "layout.blade.php", "line": "?"}}]}, "queries": {"count": 3, "nb_statements": 3, "nb_visible_statements": 3, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.00184, "accumulated_duration_str": "1.84ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `sessions` where `id` = '9zzkH57N3ClbDtxtBrJ1qDcWJzaLhV70SL6IRJQ6' limit 1", "type": "query", "params": [], "bindings": ["9zzkH57N3ClbDtxtBrJ1qDcWJzaLhV70SL6IRJQ6"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\laragon\\www\\remitify\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\remitify\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\remitify\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\laragon\\www\\remitify\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\laragon\\www\\remitify\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.244612, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\laragon\\www\\remitify\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fremitify%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php:96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "remitify", "explain": null, "start_percent": 0, "width_percent": 21.196}, {"sql": "select * from `languages` where `is_default` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/helpers.php", "file": "C:\\laragon\\www\\remitify\\app\\helpers.php", "line": 366}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\remitify\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "C:\\laragon\\www\\remitify\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\remitify\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php", "file": "C:\\laragon\\www\\remitify\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php", "line": 87}], "start": **********.247667, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "helpers.php:366", "source": {"index": 16, "namespace": null, "name": "app/helpers.php", "file": "C:\\laragon\\www\\remitify\\app\\helpers.php", "line": 366}, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fremitify%2Fapp%2Fhelpers.php:366", "ajax": false, "filename": "helpers.php", "line": "366"}, "connection": "remitify", "explain": null, "start_percent": 21.196, "width_percent": 53.804}, {"sql": "select * from `languages` where `status` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Providers/ViewServiceProvider.php", "file": "C:\\laragon\\www\\remitify\\app\\Providers\\ViewServiceProvider.php", "line": 62}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\laragon\\www\\remitify\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 162}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Concerns/ManagesEvents.php", "file": "C:\\laragon\\www\\remitify\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 178}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\laragon\\www\\remitify\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 189}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\laragon\\www\\remitify\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 160}], "start": **********.38735, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "ViewServiceProvider.php:62", "source": {"index": 15, "namespace": null, "name": "app/Providers/ViewServiceProvider.php", "file": "C:\\laragon\\www\\remitify\\app\\Providers\\ViewServiceProvider.php", "line": 62}, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fremitify%2Fapp%2FProviders%2FViewServiceProvider.php:62", "ajax": false, "filename": "ViewServiceProvider.php", "line": "62"}, "connection": "remitify", "explain": null, "start_percent": 75, "width_percent": 25}]}, "models": {"data": {"App\\Models\\Language": {"value": 1, "xdebug_link": {"url": "vscode://file/C%3A%2Flaragon%2Fwww%2Fremitify%2Fapp%2FModels%2FLanguage.php:1", "ajax": false, "filename": "Language.php", "line": "?"}}}, "count": 1, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "500 Internal Server Error", "full_url": "http://remitify.test/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate", "uri": "POST livewire/update", "controller": "App\\Livewire\\Agent\\Auth\\Register@_finishUpload<a href=\"vscode://file/C%3A%2Flaragon%2Fwww%2Fremitify%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FFeatures%2FSupportFileUploads%2FWithFileUploads.php:28\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"vscode://file/C%3A%2Flaragon%2Fwww%2Fremitify%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FFeatures%2FSupportFileUploads%2FWithFileUploads.php:28\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/livewire/livewire/src/Features/SupportFileUploads/WithFileUploads.php:28-51</a>", "middleware": "web", "duration": "1.09s", "peak_memory": "42MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-251613496 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-251613496\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1898573325 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">RMxF3qX3ZYTEIfPd2KsBSsaXkg5FlaxjW4jANBbe</span>\"\n  \"<span class=sf-dump-key>components</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"469 characters\">{&quot;data&quot;:{&quot;first_name&quot;:null,&quot;last_name&quot;:null,&quot;email&quot;:null,&quot;country&quot;:&quot;Bangladesh:+880&quot;,&quot;gender&quot;:null,&quot;phone&quot;:null,&quot;username&quot;:null,&quot;password&quot;:null,&quot;password_confirmation&quot;:null,&quot;verification&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}]},&quot;memo&quot;:{&quot;id&quot;:&quot;CNiadgtC0wt2iYBTAeyS&quot;,&quot;name&quot;:&quot;agent.auth.register&quot;,&quot;path&quot;:&quot;agent\\/register&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[&quot;4169463714-0&quot;],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;en&quot;},&quot;checksum&quot;:&quot;c85e7c1ee0f9ce7a163d9a1e1f5f76cf22f562a053953c597a5242fd80f5913f&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => []\n      \"<span class=sf-dump-key>calls</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"13 characters\">_finishUpload</span>\"\n          \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">files[]</span>\"\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"69 characters\">/xWUIw1MUvnn5CnhPkF5MlQew3j01Jn-metaMDQgLSBDb3B5IC0gQ29weS5wbmc=-.png</span>\"\n            </samp>]\n            <span class=sf-dump-index>2</span> => \"\"\n          </samp>]\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1898573325\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1226771907 data-indent-pad=\"  \"><span class=sf-dump-note>array:12</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"714 characters\">XSRF-TOKEN=eyJpdiI6IlJFRjhRMmtmM3FNTmdJdjYxbHFhYVE9PSIsInZhbHVlIjoiVGxCc21Za3I1NXlnblZRdWR1RWZSVDQxcEFWNWQ2WWNqcEM2bnRiL2FURTNSOG9USE9QZk5WN3Fxa1lMTG10bXFuRGQ2ODNlN0JRSmJMcnJnNEpOc0lObkFvVWpDUXhVVGc0K2RKVUczUUFXWTNIUSt0Y2xoSk1BVXJqaEVwUmciLCJtYWMiOiJkMDRhNDI5YmZmMTdlNzI1ZTE3Njk3MzM1N2NmNzdjOTE4ZDRjZjk0YmU4ZTEzMjAyYWEwYmQ2NTYyMGM3NzY3IiwidGFnIjoiIn0%3D; remitify_session=eyJpdiI6InFrcFU5SGpiUzNxY3FjNzNYVXNzQ3c9PSIsInZhbHVlIjoiNTI0Yko3am9zV0E0bHFyekRNL2M5Z3F5R0xraHRHdStoMy9UcTdJS1FjNmFkV21sVktkMklBTFNCS1dVQ1dzNm8xWkZpUzRtaWJqWFNjcnhHcDJrRm1ZUlNETkZBbnBveWpFMWZydFhuOWd2cEp0VEVjcFlyVjNJR3JxTmJveXoiLCJtYWMiOiJjMDc1N2EwYjFjZjEyY2U0OTEyNzQwMTQ3OTcwNGRmNzU0YWFmMWM0ZTUwNTE3NzJiOWUzZmFjYTYxYmNmMmQwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,bn;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">http://remitify.test/agent/register</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://remitify.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">779</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">close</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">remitify.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1226771907\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1573728461 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">RMxF3qX3ZYTEIfPd2KsBSsaXkg5FlaxjW4jANBbe</span>\"\n  \"<span class=sf-dump-key>remitify_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9zzkH57N3ClbDtxtBrJ1qDcWJzaLhV70SL6IRJQ6</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1573728461\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1173896747 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 25 Jun 2025 04:32:53 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1173896747\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1842927290 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">RMxF3qX3ZYTEIfPd2KsBSsaXkg5FlaxjW4jANBbe</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"35 characters\">http://remitify.test/agent/register</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1842927290\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "500 Internal Server Error", "full_url": "http://remitify.test/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate"}, "badge": "500 Internal Server Error"}}