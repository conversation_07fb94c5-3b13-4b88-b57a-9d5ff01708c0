<div class="sign-up-box">
    <div class="signup-full">
        <div class="left">
            <div class="auth-form">
                <div class="title">
                    <h4><?php echo e(__('Agent Registration')); ?></h4>
                </div>
                <form wire:submit="registerNow">
                    <div class="content">
                        <div class="content-separation">
                            <p><?php echo e(__('Basic information')); ?></p>
                        </div>
                        <div class="row g-4">
                            <div class="col-md-6">
                                <div class="td-form-group">
                                    <label class="input-label"><?php echo e(__('First Name')); ?> <span>*</span></label>
                                    <div class="input-field">
                                        <input type="text" class="form-control" wire:model="first_name">
                                    </div>
                                    <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['first_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <p class="feedback-invalid"><?php echo e($message); ?></p>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="td-form-group">
                                    <label class="input-label"><?php echo e(__('Last Name')); ?> <span>*</span></label>
                                    <div class="input-field">
                                        <input type="text" class="form-control" wire:model="last_name">
                                    </div>
                                    <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['last_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <p class="feedback-invalid"><?php echo e($message); ?></p>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="td-form-group">
                                    <label class="input-label"><?php echo e(__('Email Address')); ?> <span>*</span></label>
                                    <div class="input-field">
                                        <input type="email" class="form-control" wire:model="email">
                                    </div>
                                    <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <p class="feedback-invalid"><?php echo e($message); ?></p>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
                                </div>
                            </div>
                            <!--[if BLOCK]><![endif]--><?php if(getPageSetting('agent_username_show')): ?>
                                <div class="col-md-6">
                                    <div class="td-form-group">
                                        <label class="input-label">
                                            <?php echo e(__('Username')); ?>

                                            <!--[if BLOCK]><![endif]--><?php if(getPageSetting('agent_username_validation')): ?>
                                                <span>*</span>
                                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                        </label>
                                        <div class="input-field">
                                            <input type="text" class="form-control" wire:model="username">
                                        </div>
                                        <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['username'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <p class="feedback-invalid"><?php echo e($message); ?></p>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
                                    </div>
                                </div>
                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                            <!--[if BLOCK]><![endif]--><?php if(getPageSetting('agent_country_show')): ?>
                                <div class="col-md-6">
                                    <div class="td-form-group">
                                        <label class="input-label"><?php echo e(__('Country')); ?>

                                            <!--[if BLOCK]><![endif]--><?php if(getPageSetting('agent_country_validation')): ?>
                                                <span>*</span>
                                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                        </label>
                                        <div class="common-nice-select common-nice-select-border-2 full-width w-100"
                                            wire:ignore>
                                            <select class="nice-select-active w-100 country-select"
                                                wire:model.live="country">
                                                <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $countries; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $country): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <option value="<?php echo e($country['name']); ?>:<?php echo e($country['dial_code']); ?>"
                                                        data-flag="<?php echo e($country['flag']); ?>"
                                                        data-code="<?php echo e($country['dial_code']); ?>"
                                                        <?php if(old('country', data_get($location, 'name') . ':' . data_get($location, 'dial_code')) == $country['name'] . ':' . $country['dial_code']): echo 'selected'; endif; ?>>
                                                        <?php echo e($country['name']); ?></option>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                                            </select>
                                        </div>
                                        <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['country'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <p class="feedback-invalid"><?php echo e($message); ?></p>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
                                    </div>
                                </div>
                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                            <!--[if BLOCK]><![endif]--><?php if(getPageSetting('agent_gender_show')): ?>
                                <div class="col-md-6">
                                    <div class="td-form-group">
                                        <label class="input-label"><?php echo e(__('Gender')); ?>

                                            <!--[if BLOCK]><![endif]--><?php if(getPageSetting('agent_gender_validation')): ?>
                                                <span>*</span>
                                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                        </label>
                                        <div class="common-nice-select common-nice-select-border-2 full-width w-100"
                                            wire:ignore>
                                            <select class="nice-select-active w-100 gender-select"
                                                wire:model.live="gender">
                                                <option value="male" <?php if(old('gender') == 'male'): echo 'selected'; endif; ?>>
                                                    <?php echo e(__('Male')); ?>

                                                </option>
                                                <option value="female" <?php if(old('gender') == 'female'): echo 'selected'; endif; ?>>
                                                    <?php echo e(__('Female')); ?>

                                                </option>
                                                <option value="other" <?php if(old('gender') == 'other'): echo 'selected'; endif; ?>>
                                                    <?php echo e(__('Other')); ?>

                                                </option>
                                            </select>
                                        </div>
                                        <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['gender'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <p class="feedback-invalid"><?php echo e($message); ?></p>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
                                    </div>
                                </div>
                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                            <!--[if BLOCK]><![endif]--><?php if(getPageSetting('agent_phone_show')): ?>
                                <div class="col-md-6">
                                    <div class="td-form-group">
                                        <label class="input-label"><?php echo e(__('Phone Number')); ?>

                                            <!--[if BLOCK]><![endif]--><?php if(getPageSetting('agent_phone_validation')): ?>
                                                <span>*</span>
                                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                        </label>
                                        <div class="input-field has-left-text has-left-text-2">
                                            <input type="text" class="form-control" wire:model="phone">
                                            <div class="left-text">
                                                <div class="text"
                                                    x-text="$wire.country.split(':')[1]?.replace('*', '') || ''"></div>
                                            </div>
                                        </div>
                                        <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['phone'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <p class="feedback-invalid"><?php echo e($message); ?></p>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
                                    </div>
                                </div>
                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                            <div class="col-md-6">
                                <div class="td-form-group">
                                    <label class="input-label"><?php echo e(__('Password')); ?> <span>*</span></label>
                                    <div class="input-field">
                                        <input type="password" class="form-control" wire:model="password">
                                    </div>
                                    <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <p class="feedback-invalid"><?php echo e($message); ?></p>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="td-form-group">
                                    <label class="input-label"><?php echo e(__('Confirm Password')); ?> <span>*</span></label>
                                    <div class="input-field">
                                        <input type="password" class="form-control" wire:model="password_confirmation">
                                    </div>
                                    <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['password_confirmation'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <p class="feedback-invalid"><?php echo e($message); ?></p>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
                                </div>
                            </div>
                        </div>
                        <div class="content-separation mt-5">
                            <p><?php echo e(__('Verification information')); ?></p>
                        </div>
                        <div class="row g-4">
                            <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $this->getVerificationInfo(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $fieldKey => $field): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="col-12">
                                    <!--[if BLOCK]><![endif]--><?php if($field['type'] == 'text'): ?>
                                        <div class="td-form-group">
                                            <label class="input-label"><?php echo e($field['name']); ?> <span>*</span></label>
                                            <div class="input-field">
                                                <input type="text" class="form-control"
                                                    wire:model="verification.<?php echo e($fieldKey); ?>">
                                            </div>
                                            <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['verification.' . $field['name']];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                <p class="feedback-invalid"><?php echo e($message); ?></p>
                                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
                                        </div>
                                    <?php elseif($field['type'] == 'file'): ?>
                                        <div class="common-upload-image-system">
                                            <div class="title">
                                                <div class="left">
                                                    <p><?php echo e($field['name']); ?><sup>*</sup></p>
                                                </div>
                                            </div>
                                            <div class="my-2">
                                                <?php echo $__env->make('frontend.default.include._file_uploader', [
                                                    'label' => $field['name'],
                                                    'required' => true,
                                                    'multiple' => false,
                                                    'wire:model' => 'verification.' . $fieldKey,
                                                ], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                                            </div>
                                        </div>
                                    <?php elseif($field['type'] == 'textarea'): ?>
                                        <div class="td-form-group">
                                            <label class="input-label"><?php echo e($field['name']); ?> <span>*</span></label>
                                            <div class="input-field">
                                                <textarea class="form-control" wire:model="verification.<?php echo e($fieldKey); ?>"></textarea>
                                            </div>
                                            <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['verification.' . $field['name']];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                <p class="feedback-invalid"><?php echo e($message); ?></p>
                                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
                                        </div>
                                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                            <div class="col-12">
                                <div class="action-btn">
                                    <button type="submit" class="primary-button xl-btn w-100"
                                        wire:loading.attr="disabled" wire:target="registerNow" wire:loading.remove>
                                        <?php echo e(__('Create Account')); ?>

                                    </button>
                                    <button type="button" class="primary-button xl-btn w-100" disabled wire:loading
                                        wire:target="registerNow">
                                        <?php echo e(__('Creating Account...')); ?>

                                    </button>
                                </div>
                            </div>
                            <div class="col-12">
                                <div class="auth-switch">
                                    <p><?php echo e(__('Already have an account?')); ?> <a
                                            href="<?php echo e(route('agent.login')); ?>"><?php echo e(__('Sign in')); ?></a></p>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

    <?php
        $__scriptKey = '**********-0';
        ob_start();
    ?>
    <script>
        $('.country-select').on('change', function() {
            let value = $(this).val();
            window.Livewire.find('<?php echo e($_instance->getId()); ?>').set('country', value);
        });

        $('.gender-select').on('change', function() {
            let value = $(this).val();
            window.Livewire.find('<?php echo e($_instance->getId()); ?>').set('gender', value);
        });
    </script>
    <?php
        $__output = ob_get_clean();

        \Livewire\store($this)->push('scripts', $__output, $__scriptKey)
    ?>
<?php /**PATH C:\laragon\www\remitify\app\Providers/../../resources/views/frontend/default/agent/auth/forms/register.blade.php ENDPATH**/ ?>