<div x-data="{
    files: <?php if ((object) ('files') instanceof \Livewire\WireDirective) : ?>window.Livewire.find('<?php echo e($__livewire->getId()); ?>').entangle('<?php echo e('files'->value()); ?>')<?php echo e('files'->hasModifier('live') ? '.live' : ''); ?><?php else : ?>window.Livewire.find('<?php echo e($__livewire->getId()); ?>').entangle('<?php echo e('files'); ?>')<?php endif; ?>.defer,
    dragOver: false,
    multiple: <?php echo e($multiple ? 'true' : 'false'); ?>,
    handleDrop(event) {
        this.dragOver = false;
        const droppedFiles = event.dataTransfer.files;
        [...droppedFiles].forEach(file => this.$wire.upload('files[]', file));
    },
    handleFiles(event) {
        const selectedFiles = event.target.files;
        [...selectedFiles].forEach(file => this.$wire.upload('files[]', file));
    },
    removeFile(index) {
        this.$wire.call('removeFile', index);
    }
}" x-on:dragover.prevent="dragOver = true" x-on:dragleave.prevent="dragOver = false"
    x-on:drop.prevent="handleDrop($event)" class="common-upload-image-system">
    <div class="title mb-2">
        <div class="left">
            <p>
                <?php echo e($label); ?>

                <!--[if BLOCK]><![endif]--><?php if($required): ?>
                    <sup>*</sup>
                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
            </p>
        </div>
    </div>

    <div class="upload-thumb border rounded p-4 text-center" :class="{ 'border-blue-500': dragOver }"
        @click="$refs.fileInput.click()">
        <input type="file" :multiple="multiple" x-ref="fileInput" class="hidden"
            @change="handleFiles($event)">

        <template x-if="files.length === 0">
            <div class="upload-thumb-content">
                <h4><a href="#" @click.prevent="$refs.fileInput.click()" class="attach-file">Attach File</a> or
                    Drag & Drop</h4>
            </div>
        </template>

        <div class="upload-thumb-img grid grid-cols-3 gap-4 mt-4" x-show="files.length > 0">
            <template x-for="(file, index) in files" :key="index">
                <div class="relative border rounded p-2">
                    <span
                        class="absolute top-1 right-1 text-white bg-black bg-opacity-50 rounded-full w-5 h-5 flex items-center justify-center cursor-pointer"
                        @click.stop="removeFile(index)">×</span>

                    <template x-if="file instanceof File && file.type.startsWith('image/')">
                        <img :src="URL.createObjectURL(file)" class="w-full h-auto max-h-32 object-cover rounded">
                    </template>

                    <template x-if="!(file instanceof File) || !file.type.startsWith('image/')">
                        <div class="text-xs break-words text-center" x-text="file.name || 'Uploaded File'"></div>
                    </template>
                </div>
            </template>
        </div>
    </div>
</div>
<?php /**PATH C:\laragon\www\remitify\resources\views/frontend/default/include/_file_uploader.blade.php ENDPATH**/ ?>